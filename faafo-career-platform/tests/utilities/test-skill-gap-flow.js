#!/usr/bin/env node

const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

const BASE_URL = 'http://localhost:3000';

// Test user credentials
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

let sessionCookie = '';

async function makeRequest(endpoint, options = {}) {
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(sessionCookie && { 'Cookie': sessionCookie }),
      ...options.headers
    }
  };

  const response = await fetch(`${BASE_URL}${endpoint}`, {
    ...defaultOptions,
    ...options
  });

  // Extract session cookie from response
  if (response.headers.get('set-cookie')) {
    const cookies = response.headers.get('set-cookie');
    if (cookies.includes('next-auth.session-token')) {
      sessionCookie = cookies;
    }
  }

  return response;
}

async function testAuthentication() {
  console.log('\n🔐 Testing Authentication...');
  
  try {
    // Try to sign in
    const signInResponse = await makeRequest('/api/auth/signin', {
      method: 'POST',
      body: JSON.stringify({
        email: TEST_USER.email,
        password: TEST_USER.password,
        callbackUrl: '/'
      })
    });

    console.log(`📡 Sign-in Status: ${signInResponse.status}`);
    
    if (signInResponse.status === 200) {
      console.log('✅ Authentication successful');
      return true;
    } else {
      console.log('❌ Authentication failed - user may not exist');
      return false;
    }
  } catch (error) {
    console.error('❌ Authentication error:', error.message);
    return false;
  }
}

async function testAssessmentStatus() {
  console.log('\n📊 Testing Assessment Status...');
  
  try {
    const response = await makeRequest('/api/assessment?status=true');
    console.log(`📡 Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('📊 Assessment Status:', JSON.stringify(data, null, 2));
      return data;
    } else {
      console.log('❌ Assessment status check failed');
      return null;
    }
  } catch (error) {
    console.error('❌ Assessment status error:', error.message);
    return null;
  }
}

async function testSkillAssessments() {
  console.log('\n🎯 Testing Skill Assessments...');
  
  try {
    // Get existing assessments
    const getResponse = await makeRequest('/api/skills/assessment');
    console.log(`📡 Get Assessments Status: ${getResponse.status}`);
    
    if (getResponse.ok) {
      const data = await getResponse.json();
      console.log('📊 Existing Assessments:', JSON.stringify(data, null, 2));
      return data;
    } else {
      console.log('❌ Failed to get skill assessments');
      return null;
    }
  } catch (error) {
    console.error('❌ Skill assessments error:', error.message);
    return null;
  }
}

async function testSkillGapAnalysis() {
  console.log('\n🔍 Testing Skill Gap Analysis...');
  
  try {
    const analysisRequest = {
      currentSkills: [
        {
          skillName: 'JavaScript',
          selfRating: 7,
          confidenceLevel: 8,
          yearsOfExperience: 3
        },
        {
          skillName: 'React',
          selfRating: 6,
          confidenceLevel: 7,
          yearsOfExperience: 2
        }
      ],
      targetCareerPath: {
        careerPathName: 'Full Stack Developer',
        targetLevel: 'INTERMEDIATE'
      },
      preferences: {
        timeframe: 'SIX_MONTHS',
        hoursPerWeek: 10,
        learningStyle: ['hands-on'],
        budget: 'FREEMIUM',
        focusAreas: ['Technical Skills']
      },
      includeMarketData: true,
      includePersonalizedPaths: true
    };

    const response = await makeRequest('/api/ai/skills-analysis/comprehensive', {
      method: 'POST',
      body: JSON.stringify(analysisRequest)
    });

    console.log(`📡 Analysis Status: ${response.status}`);
    
    if (response.ok) {
      const data = await response.json();
      console.log('📊 Analysis Result:', JSON.stringify(data, null, 2));
      return data;
    } else {
      const errorData = await response.json();
      console.log('❌ Analysis failed:', JSON.stringify(errorData, null, 2));
      return null;
    }
  } catch (error) {
    console.error('❌ Skill gap analysis error:', error.message);
    return null;
  }
}

async function runCompleteTest() {
  console.log('🚀 Starting Complete Skill Gap Flow Test...\n');

  // Test authentication
  const authSuccess = await testAuthentication();
  if (!authSuccess) {
    console.log('\n❌ Skipping authenticated tests due to auth failure');
    console.log('💡 Note: This is expected if test user doesn\'t exist');
    return;
  }

  // Test assessment status
  const assessmentStatus = await testAssessmentStatus();
  
  // Test skill assessments
  const skillAssessments = await testSkillAssessments();
  
  // Test skill gap analysis
  const analysisResult = await testSkillGapAnalysis();

  console.log('\n📋 Test Summary:');
  console.log(`✅ Authentication: ${authSuccess ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Assessment Status: ${assessmentStatus ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Skill Assessments: ${skillAssessments ? 'PASS' : 'FAIL'}`);
  console.log(`✅ Gap Analysis: ${analysisResult ? 'PASS' : 'FAIL'}`);
  
  console.log('\n🎉 Complete flow test finished!');
}

runCompleteTest().catch(console.error);
