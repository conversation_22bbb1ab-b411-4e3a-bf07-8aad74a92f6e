#!/usr/bin/env node

const { chromium } = require('playwright');

const BASE_URL = 'http://localhost:3000';
const TEST_USER = {
  email: '<EMAIL>',
  password: 'TestPassword123!'
};

async function runE2ETest() {
  console.log('🚀 Starting End-to-End Skill Gap Analyzer Test...\n');

  const browser = await chromium.launch({ headless: false, slowMo: 1000 });
  const context = await browser.newContext();
  const page = await context.newPage();

  try {
    // Step 1: Navigate to login page
    console.log('🔐 Step 1: Logging in...');
    await page.goto(`${BASE_URL}/login`);
    await page.waitForLoadState('networkidle');

    // Fill login form
    await page.fill('#email', TEST_USER.email);
    await page.fill('#password', TEST_USER.password);
    await page.click('button[type="submit"]');
    await page.waitForLoadState('networkidle');

    // Check if login was successful
    const currentUrl = page.url();
    if (currentUrl.includes('/login')) {
      console.log('❌ Login failed - still on login page');
      return false;
    }
    console.log('✅ Login successful');

    // Step 2: Navigate to Skill Gap Analyzer
    console.log('\n🎯 Step 2: Navigating to Skill Gap Analyzer...');
    await page.goto(`${BASE_URL}/skills/gap-analyzer`);
    await page.waitForLoadState('networkidle');

    // Check if page loaded correctly
    const title = await page.textContent('h1');
    if (!title.includes('Skill Gap Analyzer')) {
      console.log('❌ Failed to load Skill Gap Analyzer page');
      return false;
    }
    console.log('✅ Skill Gap Analyzer page loaded');

    // Step 3: Check Assessment Tab
    console.log('\n📊 Step 3: Testing Assessment Tab...');
    await page.click('[data-value="assess"]');
    await page.waitForTimeout(1000);

    // Look for existing assessments or assessment form
    const assessmentContent = await page.textContent('.space-y-6');
    console.log('📊 Assessment tab content loaded');

    // Step 4: Check Analysis Tab
    console.log('\n🔍 Step 4: Testing Analysis Tab...');
    await page.click('[data-value="analyze"]');
    await page.waitForTimeout(1000);

    // Check if analysis form is visible
    const analysisForm = await page.isVisible('input[placeholder*="Full Stack Developer"]');
    if (!analysisForm) {
      console.log('❌ Analysis form not visible');
      return false;
    }
    console.log('✅ Analysis form is visible');

    // Fill analysis form
    await page.fill('input[placeholder*="Full Stack Developer"]', 'Full Stack Developer');
    await page.selectOption('select', 'INTERMEDIATE');
    
    // Check if analyze button is enabled
    const analyzeButton = page.locator('button:has-text("Analyze Skill Gaps")');
    const isEnabled = await analyzeButton.isEnabled();
    if (!isEnabled) {
      console.log('❌ Analyze button is not enabled');
      return false;
    }
    console.log('✅ Analyze button is enabled');

    // Step 5: Test Analysis (if we have data)
    console.log('\n⚡ Step 5: Testing Analysis...');
    
    // Click analyze button
    await analyzeButton.click();
    await page.waitForTimeout(2000);

    // Check for loading state or results
    const isAnalyzing = await page.isVisible('text=Analyzing...');
    if (isAnalyzing) {
      console.log('🔄 Analysis is running...');
      // Wait for analysis to complete (with timeout)
      await page.waitForSelector('text=Analyzing...', { state: 'detached', timeout: 30000 });
    }

    // Check if we got results or error
    const hasResults = await page.isVisible('[data-value="results"]');
    const hasError = await page.isVisible('text=Authentication required');
    
    if (hasError) {
      console.log('⚠️  Authentication required for analysis (expected)');
    } else if (hasResults) {
      console.log('✅ Analysis completed successfully');
      
      // Step 6: Check Results Tab
      console.log('\n📈 Step 6: Testing Results Tab...');
      await page.click('[data-value="results"]');
      await page.waitForTimeout(1000);
      
      const resultsContent = await page.textContent('.space-y-6');
      if (resultsContent.includes('No Analysis Results')) {
        console.log('ℹ️  No analysis results available');
      } else {
        console.log('✅ Analysis results displayed');
      }
    }

    // Step 7: Test Navigation and UI Elements
    console.log('\n🧭 Step 7: Testing Navigation...');
    
    // Test tab switching
    await page.click('[data-value="assess"]');
    await page.waitForTimeout(500);
    await page.click('[data-value="analyze"]');
    await page.waitForTimeout(500);
    await page.click('[data-value="results"]');
    await page.waitForTimeout(500);
    
    console.log('✅ Tab navigation working');

    // Step 8: Check for Console Errors
    console.log('\n🐛 Step 8: Checking for Console Errors...');
    const logs = [];
    page.on('console', msg => {
      if (msg.type() === 'error') {
        logs.push(msg.text());
      }
    });

    await page.reload();
    await page.waitForLoadState('networkidle');
    
    if (logs.length > 0) {
      console.log('⚠️  Console errors found:');
      logs.forEach(log => console.log(`   - ${log}`));
    } else {
      console.log('✅ No console errors found');
    }

    console.log('\n🎉 End-to-End Test Completed Successfully!');
    return true;

  } catch (error) {
    console.error('❌ E2E Test failed:', error.message);
    return false;
  } finally {
    await browser.close();
  }
}

// Check if Playwright is installed
async function checkPlaywright() {
  try {
    require('playwright');
    return true;
  } catch (error) {
    console.log('❌ Playwright not found. Installing...');
    const { execSync } = require('child_process');
    try {
      execSync('npm install playwright', { stdio: 'inherit' });
      execSync('npx playwright install chromium', { stdio: 'inherit' });
      return true;
    } catch (installError) {
      console.error('❌ Failed to install Playwright:', installError.message);
      return false;
    }
  }
}

async function main() {
  const playwrightReady = await checkPlaywright();
  if (!playwrightReady) {
    console.log('❌ Cannot run E2E tests without Playwright');
    return;
  }

  const success = await runE2ETest();
  process.exit(success ? 0 : 1);
}

main().catch(console.error);
