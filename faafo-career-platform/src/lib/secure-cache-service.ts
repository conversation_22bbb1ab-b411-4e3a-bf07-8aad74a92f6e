/**
 * Secure Cache Service with User Session Validation
 * Prevents cache key collisions and unauthorized access to cached data
 */

import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from './auth';
import { cacheService } from './services/cacheService';
import crypto from 'crypto';

interface SecureCacheOptions {
  ttl?: number;
  requireSession?: boolean;
  includeSessionId?: boolean;
  validateUserAccess?: boolean;
}

interface CacheMetadata {
  userId: string;
  sessionId?: string;
  timestamp: number;
  version: string;
  checksum: string;
}

export class SecureCacheService {
  private static readonly CACHE_VERSION = 'v2.0';
  private static readonly DEFAULT_TTL = 3600; // 1 hour

  /**
   * Generate a secure cache key with user isolation and collision prevention
   */
  static generateSecureKey(
    type: string,
    userId: string,
    sessionId: string | null,
    ...params: string[]
  ): string {
    // Sanitize all inputs
    const sanitizedType = this.sanitizeInput(type);
    const sanitizedUserId = this.sanitizeInput(userId);
    const sanitizedSessionId = sessionId ? this.sanitizeInput(sessionId) : 'no-session';
    const sanitizedParams = params.map(p => this.sanitizeInput(p));

    // Create a unique identifier
    const timestamp = Date.now();
    const nonce = crypto.randomBytes(4).toString('hex');
    
    // Generate checksum for integrity
    const dataToHash = [sanitizedType, sanitizedUserId, sanitizedSessionId, ...sanitizedParams].join('|');
    const checksum = crypto.createHash('sha256').update(dataToHash).digest('hex').substring(0, 16);

    return `secure:${this.CACHE_VERSION}:${sanitizedType}:${sanitizedUserId}:${sanitizedSessionId}:${checksum}:${nonce}`;
  }

  /**
   * Set data in cache with security metadata
   */
  static async setSecure<T>(
    request: NextRequest,
    type: string,
    data: T,
    params: string[] = [],
    options: SecureCacheOptions = {}
  ): Promise<boolean> {
    try {
      const session = await getServerSession(authOptions);
      
      if (options.requireSession !== false && !session?.user?.id) {
        console.warn('SecureCache: Attempted to cache data without valid session');
        return false;
      }

      const userId = session?.user?.id || 'anonymous';
      const sessionId = options.includeSessionId ? this.extractSessionId(request) : null;
      
      // Generate secure cache key
      const cacheKey = this.generateSecureKey(type, userId, sessionId, ...params);
      
      // Create metadata for validation
      const metadata: CacheMetadata = {
        userId,
        sessionId: sessionId || undefined,
        timestamp: Date.now(),
        version: this.CACHE_VERSION,
        checksum: this.generateDataChecksum(data)
      };

      // Wrap data with metadata
      const secureData = {
        metadata,
        data
      };

      await cacheService.setJSON(cacheKey, secureData, options.ttl || this.DEFAULT_TTL);
      
      console.log('SecureCache: Data cached successfully', {
        type,
        userId: userId.substring(0, 10) + '...',
        keyLength: cacheKey.length
      });

      return true;
    } catch (error) {
      console.error('SecureCache: Failed to set cache data', error);
      return false;
    }
  }

  /**
   * Get data from cache with security validation
   */
  static async getSecure<T>(
    request: NextRequest,
    type: string,
    params: string[] = [],
    options: SecureCacheOptions = {}
  ): Promise<T | null> {
    try {
      const session = await getServerSession(authOptions);
      
      if (options.requireSession !== false && !session?.user?.id) {
        return null;
      }

      const userId = session?.user?.id || 'anonymous';
      const sessionId = options.includeSessionId ? this.extractSessionId(request) : null;
      
      // Generate the same secure cache key
      const cacheKey = this.generateSecureKey(type, userId, sessionId, ...params);
      
      // Retrieve cached data
      const cachedData = await cacheService.getJSON<{
        metadata: CacheMetadata;
        data: T;
      }>(cacheKey);

      if (!cachedData) {
        return null;
      }

      // Validate metadata
      if (!this.validateMetadata(cachedData.metadata, userId, sessionId, options)) {
        console.warn('SecureCache: Metadata validation failed, removing invalid cache entry');
        await cacheService.delete(cacheKey);
        return null;
      }

      // Validate data integrity
      const expectedChecksum = this.generateDataChecksum(cachedData.data);
      if (cachedData.metadata.checksum !== expectedChecksum) {
        console.warn('SecureCache: Data integrity check failed, removing corrupted cache entry');
        await cacheService.delete(cacheKey);
        return null;
      }

      console.log('SecureCache: Data retrieved successfully', {
        type,
        userId: userId.substring(0, 10) + '...',
        age: Date.now() - cachedData.metadata.timestamp
      });

      return cachedData.data;
    } catch (error) {
      console.error('SecureCache: Failed to get cache data', error);
      return null;
    }
  }

  /**
   * Delete cached data
   */
  static async deleteSecure(
    request: NextRequest,
    type: string,
    params: string[] = [],
    options: SecureCacheOptions = {}
  ): Promise<boolean> {
    try {
      const session = await getServerSession(authOptions);
      const userId = session?.user?.id || 'anonymous';
      const sessionId = options.includeSessionId ? this.extractSessionId(request) : null;
      
      const cacheKey = this.generateSecureKey(type, userId, sessionId, ...params);
      await cacheService.delete(cacheKey);
      
      return true;
    } catch (error) {
      console.error('SecureCache: Failed to delete cache data', error);
      return false;
    }
  }

  /**
   * Sanitize input to prevent cache key injection
   */
  private static sanitizeInput(input: string): string {
    return input
      .replace(/[^a-zA-Z0-9_-]/g, '_')
      .substring(0, 100)
      .toLowerCase();
  }

  /**
   * Extract session ID from request
   */
  private static extractSessionId(request: NextRequest): string | null {
    // Try to get session ID from various sources
    const sessionCookie = request.cookies.get('next-auth.session-token')?.value ||
                         request.cookies.get('__Secure-next-auth.session-token')?.value;
    
    if (sessionCookie) {
      // Use first 16 characters of session token as session ID
      return crypto.createHash('sha256').update(sessionCookie).digest('hex').substring(0, 16);
    }

    return null;
  }

  /**
   * Generate checksum for data integrity
   */
  private static generateDataChecksum<T>(data: T): string {
    const dataString = JSON.stringify(data);
    return crypto.createHash('sha256').update(dataString).digest('hex').substring(0, 16);
  }

  /**
   * Validate cache metadata
   */
  private static validateMetadata(
    metadata: CacheMetadata,
    userId: string,
    sessionId: string | null,
    options: SecureCacheOptions
  ): boolean {
    // Check version compatibility
    if (metadata.version !== this.CACHE_VERSION) {
      return false;
    }

    // Check user access
    if (options.validateUserAccess !== false && metadata.userId !== userId) {
      return false;
    }

    // Check session if required
    if (options.includeSessionId && metadata.sessionId !== sessionId) {
      return false;
    }

    // Check age (max 24 hours regardless of TTL)
    const maxAge = 24 * 60 * 60 * 1000; // 24 hours
    if (Date.now() - metadata.timestamp > maxAge) {
      return false;
    }

    return true;
  }

  /**
   * Clear all cache entries for a user (for logout/security)
   */
  static async clearUserCache(userId: string): Promise<void> {
    try {
      // This would require a more sophisticated cache implementation
      // For now, we'll implement this as a future enhancement
      console.log('SecureCache: User cache clear requested for user', userId.substring(0, 10) + '...');
      
      // TODO: Implement pattern-based cache clearing
      // This would require Redis SCAN or similar functionality
    } catch (error) {
      console.error('SecureCache: Failed to clear user cache', error);
    }
  }
}

export default SecureCacheService;
