/**
 * Enhanced Fallback Service
 * Provides sophisticated algorithmic fallbacks when AI services are unavailable
 */

import { AssessmentResponse, AssessmentInsights } from './assessmentScoring';
import { AlgorithmicAssessmentService, AlgorithmicMatchResult } from './algorithmicAssessmentService';

export interface FallbackInsights {
  careerRecommendations: FallbackCareerRecommendation[];
  skillGapAnalysis: FallbackSkillGap[];
  learningPathRecommendations: FallbackLearningPath;
  marketInsights: FallbackMarketInsights;
  personalizedAdvice: string[];
  confidenceScore: number;
  fallbackReason: string;
  generatedAt: string;
}

export interface FallbackCareerRecommendation {
  careerPath: string;
  matchPercentage: number;
  reasoning: string[];
  salaryRange: { min: number; max: number; currency: string };
  marketOutlook: string;
  transitionDifficulty: 'LOW' | 'MEDIUM' | 'HIGH';
  timeToTransition: string;
  keySkillsNeeded: string[];
  successFactors: string[];
  potentialChallenges: string[];
}

export interface FallbackSkillGap {
  skill: string;
  currentLevel: number;
  targetLevel: number;
  priority: 'HIGH' | 'MEDIUM' | 'LOW';
  learningTime: string;
  recommendedApproach: string;
  marketValue: number; // 1-10
}

export interface FallbackLearningPath {
  totalDuration: string;
  phases: FallbackLearningPhase[];
  weeklyCommitment: string;
  milestones: FallbackMilestone[];
  adaptationStrategy: string;
}

export interface FallbackLearningPhase {
  phase: number;
  name: string;
  duration: string;
  focus: string[];
  deliverables: string[];
  successCriteria: string[];
}

export interface FallbackMilestone {
  week: number;
  title: string;
  description: string;
  measurableOutcome: string;
}

export interface FallbackMarketInsights {
  industryTrends: string[];
  emergingSkills: string[];
  salaryTrends: string;
  jobMarketHealth: 'EXCELLENT' | 'GOOD' | 'FAIR' | 'CHALLENGING';
  geographicOpportunities: string[];
  automationImpact: string;
}

export class EnhancedFallbackService {
  /**
   * Generate comprehensive fallback insights using algorithmic approaches
   */
  static async generateFallbackInsights(
    responses: AssessmentResponse,
    insights: AssessmentInsights,
    fallbackReason: string = 'AI service unavailable'
  ): Promise<FallbackInsights> {
    // Use algorithmic assessment service for sophisticated matching
    const algorithmicMatches = await AlgorithmicAssessmentService.generateCareerRecommendations(
      responses,
      insights
    );

    // Convert algorithmic matches to fallback format
    const careerRecommendations = this.convertToFallbackRecommendations(algorithmicMatches);

    // Generate skill gap analysis
    const skillGapAnalysis = this.generateSkillGapAnalysis(algorithmicMatches, insights);

    // Create learning path recommendations
    const learningPathRecommendations = this.generateLearningPathRecommendations(
      algorithmicMatches,
      insights,
      responses
    );

    // Generate market insights
    const marketInsights = this.generateMarketInsights(algorithmicMatches, insights);

    // Create personalized advice
    const personalizedAdvice = this.generatePersonalizedAdvice(
      algorithmicMatches,
      insights,
      responses
    );

    // Calculate confidence score
    const confidenceScore = this.calculateFallbackConfidence(algorithmicMatches, insights);

    return {
      careerRecommendations,
      skillGapAnalysis,
      learningPathRecommendations,
      marketInsights,
      personalizedAdvice,
      confidenceScore,
      fallbackReason,
      generatedAt: new Date().toISOString()
    };
  }

  /**
   * Convert algorithmic matches to fallback recommendation format
   */
  private static convertToFallbackRecommendations(
    algorithmicMatches: AlgorithmicMatchResult[]
  ): FallbackCareerRecommendation[] {
    return algorithmicMatches.slice(0, 5).map(match => ({
      careerPath: match.careerPath.name,
      matchPercentage: match.matchScore,
      reasoning: match.reasoning,
      salaryRange: {
        min: match.careerPath.salaryRange.entry,
        max: match.careerPath.salaryRange.senior,
        currency: match.careerPath.salaryRange.currency
      },
      marketOutlook: this.formatMarketOutlook(match.careerPath.marketMetrics.growthRate),
      transitionDifficulty: this.mapDifficultyLevel(match.careerPath.transitionFactors.difficultyScore),
      timeToTransition: match.estimatedTimeline,
      keySkillsNeeded: match.skillGaps.slice(0, 5).map(gap => gap.skill),
      successFactors: this.generateSuccessFactors(match),
      potentialChallenges: this.generatePotentialChallenges(match)
    }));
  }

  /**
   * Generate comprehensive skill gap analysis
   */
  private static generateSkillGapAnalysis(
    algorithmicMatches: AlgorithmicMatchResult[],
    insights: AssessmentInsights
  ): FallbackSkillGap[] {
    const allSkillGaps = new Map<string, FallbackSkillGap>();

    // Aggregate skill gaps from top career matches
    algorithmicMatches.slice(0, 3).forEach(match => {
      match.skillGaps.forEach(gap => {
        if (!allSkillGaps.has(gap.skill) || allSkillGaps.get(gap.skill)!.priority === 'LOW') {
          const marketValue = this.calculateSkillMarketValue(gap.skill);
          
          allSkillGaps.set(gap.skill, {
            skill: gap.skill,
            currentLevel: gap.currentLevel,
            targetLevel: gap.requiredLevel,
            priority: gap.priority,
            learningTime: gap.estimatedLearningTime,
            recommendedApproach: this.getRecommendedLearningApproach(gap.skill, gap.priority),
            marketValue
          });
        }
      });
    });

    return Array.from(allSkillGaps.values())
      .sort((a, b) => {
        const priorityOrder = { HIGH: 3, MEDIUM: 2, LOW: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      })
      .slice(0, 10); // Top 10 skill gaps
  }

  /**
   * Generate learning path recommendations
   */
  private static generateLearningPathRecommendations(
    algorithmicMatches: AlgorithmicMatchResult[],
    insights: AssessmentInsights,
    responses: AssessmentResponse
  ): FallbackLearningPath {
    const topMatch = algorithmicMatches[0];
    if (!topMatch) {
      return this.getDefaultLearningPath();
    }

    const phases = this.createLearningPhases(topMatch, insights);
    const milestones = this.createLearningMilestones(phases);
    const weeklyCommitment = this.calculateWeeklyCommitment(insights, responses);

    return {
      totalDuration: topMatch.estimatedTimeline,
      phases,
      weeklyCommitment,
      milestones,
      adaptationStrategy: this.generateAdaptationStrategy(insights)
    };
  }

  /**
   * Generate market insights
   */
  private static generateMarketInsights(
    algorithmicMatches: AlgorithmicMatchResult[],
    insights: AssessmentInsights
  ): FallbackMarketInsights {
    const topCareers = algorithmicMatches.slice(0, 3);
    
    return {
      industryTrends: this.extractIndustryTrends(topCareers),
      emergingSkills: this.identifyEmergingSkills(topCareers),
      salaryTrends: this.analyzeSalaryTrends(topCareers),
      jobMarketHealth: this.assessJobMarketHealth(topCareers),
      geographicOpportunities: this.identifyGeographicOpportunities(topCareers),
      automationImpact: this.assessAutomationImpact(topCareers)
    };
  }

  /**
   * Generate personalized advice
   */
  private static generatePersonalizedAdvice(
    algorithmicMatches: AlgorithmicMatchResult[],
    insights: AssessmentInsights,
    responses: AssessmentResponse
  ): string[] {
    const advice: string[] = [];
    const topMatch = algorithmicMatches[0];

    // Financial readiness advice
    if (insights.scores.financialReadiness < 3) {
      advice.push('Build an emergency fund covering 3-6 months of expenses before making a career transition');
      advice.push('Consider part-time learning while maintaining current income to reduce financial risk');
    }

    // Skill development advice
    if (topMatch && topMatch.skillGaps.length > 3) {
      advice.push('Focus on developing 1-2 high-priority skills at a time rather than trying to learn everything simultaneously');
      advice.push('Create a portfolio project that demonstrates your growing skills to potential employers');
    }

    // Timeline advice
    if (insights.scores.urgencyLevel >= 4 && topMatch?.matchScore < 70) {
      advice.push('Consider interim roles or freelance opportunities to gain experience while building toward your target career');
    }

    // Support system advice
    if (insights.scores.supportLevel < 3) {
      advice.push('Join professional communities and online forums related to your target career for networking and support');
      advice.push('Consider finding a mentor in your target field to guide your transition');
    }

    // Confidence building advice
    if (insights.scores.skillsConfidence < 60) {
      advice.push('Start with small, achievable learning goals to build confidence and momentum');
      advice.push('Document your learning progress to track improvement and boost confidence');
    }

    // Ensure we always have some advice
    if (advice.length === 0) {
      advice.push('Focus on continuous learning and skill development in your chosen field');
      advice.push('Network with professionals in your target industry to gain insights and opportunities');
      advice.push('Create a structured learning plan with specific milestones and deadlines');
      advice.push('Consider taking on projects or volunteer work to gain practical experience');
    }

    return advice;
  }

  /**
   * Calculate fallback confidence score
   */
  private static calculateFallbackConfidence(
    algorithmicMatches: AlgorithmicMatchResult[],
    insights: AssessmentInsights
  ): number {
    if (algorithmicMatches.length === 0) return 50;

    const topMatch = algorithmicMatches[0];
    const baseConfidence = topMatch.confidenceLevel;
    
    // Adjust based on data quality
    let adjustedConfidence = baseConfidence;
    
    if (insights.topSkills.length >= 3) adjustedConfidence += 5;
    if (insights.scores.readinessScore >= 70) adjustedConfidence += 10;
    if (algorithmicMatches.length >= 3) adjustedConfidence += 5;
    
    return Math.min(95, Math.max(60, adjustedConfidence)); // Clamp between 60-95
  }

  // Helper methods
  private static formatMarketOutlook(growthRate: number): string {
    if (growthRate >= 20) return `${growthRate}% (Much faster than average)`;
    if (growthRate >= 10) return `${growthRate}% (Faster than average)`;
    if (growthRate >= 5) return `${growthRate}% (As fast as average)`;
    return `${growthRate}% (Slower than average)`;
  }

  private static mapDifficultyLevel(difficultyScore: number): 'LOW' | 'MEDIUM' | 'HIGH' {
    if (difficultyScore <= 3) return 'LOW';
    if (difficultyScore <= 6) return 'MEDIUM';
    return 'HIGH';
  }

  private static generateSuccessFactors(match: AlgorithmicMatchResult): string[] {
    const factors = ['Strong analytical thinking', 'Continuous learning mindset'];
    
    if (match.matchFactors.skillAlignment >= 70) {
      factors.push('Good foundation in required skills');
    }
    
    if (match.matchFactors.transitionFeasibility >= 70) {
      factors.push('Favorable transition conditions');
    }
    
    return factors;
  }

  private static generatePotentialChallenges(match: AlgorithmicMatchResult): string[] {
    const challenges = [];
    
    if (match.skillGaps.length > 3) {
      challenges.push('Significant skill development required');
    }
    
    if (match.matchFactors.transitionFeasibility < 60) {
      challenges.push('Complex transition timeline');
    }
    
    if (match.careerPath.marketMetrics.competitionLevel >= 7) {
      challenges.push('Competitive job market');
    }
    
    return challenges.length > 0 ? challenges : ['Market competition', 'Keeping skills current'];
  }

  private static calculateSkillMarketValue(skill: string): number {
    // Simplified market value calculation
    const highValueSkills = ['AI/ML', 'Cloud Computing', 'Cybersecurity', 'Data Science'];
    const mediumValueSkills = ['Web Development', 'Mobile Development', 'Project Management'];
    
    if (highValueSkills.some(hvs => skill.toLowerCase().includes(hvs.toLowerCase()))) return 9;
    if (mediumValueSkills.some(mvs => skill.toLowerCase().includes(mvs.toLowerCase()))) return 7;
    return 5;
  }

  private static getRecommendedLearningApproach(skill: string, priority: 'HIGH' | 'MEDIUM' | 'LOW'): string {
    if (priority === 'HIGH') {
      return 'Intensive bootcamp or structured course with hands-on projects';
    }
    if (priority === 'MEDIUM') {
      return 'Online courses combined with practical application';
    }
    return 'Self-paced learning with periodic skill assessments';
  }

  private static getDefaultLearningPath(): FallbackLearningPath {
    return {
      totalDuration: '6-12 months',
      phases: [
        {
          phase: 1,
          name: 'Foundation Building',
          duration: '4-6 weeks',
          focus: ['Core concepts', 'Basic tools'],
          deliverables: ['Complete introductory course', 'Build first project'],
          successCriteria: ['Pass skill assessment', 'Demonstrate basic competency']
        }
      ],
      weeklyCommitment: '10-15 hours',
      milestones: [
        {
          week: 4,
          title: 'Foundation Complete',
          description: 'Basic skills established',
          measurableOutcome: 'Score 70%+ on skill assessment'
        }
      ],
      adaptationStrategy: 'Regular progress reviews with plan adjustments'
    };
  }

  // Additional helper methods would be implemented here...
  private static createLearningPhases(match: AlgorithmicMatchResult, insights: AssessmentInsights): FallbackLearningPhase[] {
    // Implementation would create detailed learning phases
    return [];
  }

  private static createLearningMilestones(phases: FallbackLearningPhase[]): FallbackMilestone[] {
    // Implementation would create milestones from phases
    return [];
  }

  private static calculateWeeklyCommitment(insights: AssessmentInsights, responses: AssessmentResponse): string {
    // Calculate based on user availability and urgency
    return '10-15 hours';
  }

  private static generateAdaptationStrategy(insights: AssessmentInsights): string {
    return 'Regular progress reviews with plan adjustments based on learning pace and market changes';
  }

  private static extractIndustryTrends(topCareers: AlgorithmicMatchResult[]): string[] {
    return ['Remote work adoption', 'AI integration', 'Sustainability focus'];
  }

  private static identifyEmergingSkills(topCareers: AlgorithmicMatchResult[]): string[] {
    return ['AI/ML fundamentals', 'Cloud platforms', 'Data analysis'];
  }

  private static analyzeSalaryTrends(topCareers: AlgorithmicMatchResult[]): string {
    return 'Salaries growing 5-15% annually in tech fields';
  }

  private static assessJobMarketHealth(topCareers: AlgorithmicMatchResult[]): 'EXCELLENT' | 'GOOD' | 'FAIR' | 'CHALLENGING' {
    return 'GOOD';
  }

  private static identifyGeographicOpportunities(topCareers: AlgorithmicMatchResult[]): string[] {
    return ['Major tech hubs', 'Remote opportunities', 'Emerging markets'];
  }

  private static assessAutomationImpact(topCareers: AlgorithmicMatchResult[]): string {
    return 'Low to moderate automation risk with focus on human-AI collaboration';
  }
}
