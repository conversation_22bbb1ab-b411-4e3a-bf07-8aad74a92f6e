/**
 * Comprehensive Security Validator
 * Provides enhanced input validation, threat detection, and security checks
 */

import { NextRequest } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from './auth';
import { z } from 'zod';

interface SecurityThreat {
  type: string;
  severity: 'low' | 'medium' | 'high' | 'critical';
  description: string;
  pattern?: string;
}

interface ValidationResult {
  isValid: boolean;
  threats: SecurityThreat[];
  sanitizedData?: any;
  riskScore: number;
}

interface SecurityContext {
  isAuthenticated: boolean;
  userId?: string;
  userRole?: string;
  ipAddress: string;
  userAgent: string;
  requestPath: string;
  method: string;
}

export class ComprehensiveSecurityValidator {
  private static readonly SQL_INJECTION_PATTERNS = [
    /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/i,
    /(--|\/\*|\*\/|;|\||&)/,
    /(\b(OR|AND)\b.*=.*)/i,
    /(INFORMATION_SCHEMA|SYSOBJECTS|SYSCOLUMNS)/i,
    /(WAITFOR|DELAY|BENCHMARK)/i
  ];

  private static readonly XSS_PATTERNS = [
    /<script[^>]*>.*?<\/script>/gi,
    /<iframe[^>]*>.*?<\/iframe>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /<img[^>]*src\s*=\s*["']?javascript:/gi,
    /eval\s*\(/gi,
    /expression\s*\(/gi
  ];

  private static readonly COMMAND_INJECTION_PATTERNS = [
    /[;&|`$(){}[\]]/,
    /\b(cat|ls|pwd|whoami|id|uname|ps|netstat|ifconfig|ping|wget|curl|nc|telnet|ssh|ftp)\b/i,
    /\.\.\//,
    /\/etc\/passwd|\/etc\/shadow|\/proc\/|\/sys\//,
    /cmd\.exe|powershell|bash|sh|zsh/i
  ];

  private static readonly PATH_TRAVERSAL_PATTERNS = [
    /\.\.\//,
    /\.\.\\/,
    /%2e%2e%2f/i,
    /%2e%2e%5c/i,
    /\.\.%2f/i,
    /\.\.%5c/i
  ];

  private static readonly LDAP_INJECTION_PATTERNS = [
    /[()&|!]/,
    /\*.*\*/,
    /\(\|\(/,
    /\)&\(/
  ];

  /**
   * Comprehensive input validation with threat detection
   */
  static async validateInput(
    request: NextRequest,
    data: any,
    schema?: z.ZodSchema
  ): Promise<ValidationResult> {
    const context = await this.buildSecurityContext(request);
    const threats: SecurityThreat[] = [];
    let riskScore = 0;

    try {
      // 1. Schema validation if provided
      if (schema) {
        const schemaResult = schema.safeParse(data);
        if (!schemaResult.success) {
          threats.push({
            type: 'schema_validation',
            severity: 'medium',
            description: 'Input does not match expected schema',
            pattern: schemaResult.error.message
          });
          riskScore += 30;
        }
      }

      // 2. Deep security scanning
      const securityThreats = this.scanForThreats(data, context);
      threats.push(...securityThreats);
      riskScore += securityThreats.reduce((sum, threat) => {
        return sum + this.getThreatScore(threat.severity);
      }, 0);

      // 3. Context-based validation
      const contextThreats = this.validateContext(context, data);
      threats.push(...contextThreats);
      riskScore += contextThreats.reduce((sum, threat) => {
        return sum + this.getThreatScore(threat.severity);
      }, 0);

      // 4. Sanitize data if validation passes
      const sanitizedData = this.sanitizeData(data, threats);

      const isValid = riskScore < 50 && !threats.some(t => t.severity === 'critical');

      return {
        isValid,
        threats,
        sanitizedData: isValid ? sanitizedData : undefined,
        riskScore: Math.min(riskScore, 100)
      };

    } catch (error) {
      console.error('Security validation error:', error);
      return {
        isValid: false,
        threats: [{
          type: 'validation_error',
          severity: 'high',
          description: 'Security validation failed due to internal error'
        }],
        riskScore: 100
      };
    }
  }

  /**
   * Scan for security threats in data
   */
  private static scanForThreats(data: any, context: SecurityContext): SecurityThreat[] {
    const threats: SecurityThreat[] = [];
    const dataString = this.flattenToString(data);

    // SQL Injection detection
    for (const pattern of this.SQL_INJECTION_PATTERNS) {
      if (pattern.test(dataString)) {
        threats.push({
          type: 'sql_injection',
          severity: 'critical',
          description: 'Potential SQL injection attempt detected',
          pattern: pattern.toString()
        });
      }
    }

    // XSS detection
    for (const pattern of this.XSS_PATTERNS) {
      if (pattern.test(dataString)) {
        threats.push({
          type: 'xss',
          severity: 'high',
          description: 'Potential XSS attack detected',
          pattern: pattern.toString()
        });
      }
    }

    // Command injection detection
    for (const pattern of this.COMMAND_INJECTION_PATTERNS) {
      if (pattern.test(dataString)) {
        threats.push({
          type: 'command_injection',
          severity: 'critical',
          description: 'Potential command injection detected',
          pattern: pattern.toString()
        });
      }
    }

    // Path traversal detection
    for (const pattern of this.PATH_TRAVERSAL_PATTERNS) {
      if (pattern.test(dataString)) {
        threats.push({
          type: 'path_traversal',
          severity: 'high',
          description: 'Potential path traversal attack detected',
          pattern: pattern.toString()
        });
      }
    }

    // LDAP injection detection
    for (const pattern of this.LDAP_INJECTION_PATTERNS) {
      if (pattern.test(dataString)) {
        threats.push({
          type: 'ldap_injection',
          severity: 'high',
          description: 'Potential LDAP injection detected',
          pattern: pattern.toString()
        });
      }
    }

    // Additional checks
    this.checkForAnomalies(dataString, threats);

    return threats;
  }

  /**
   * Validate security context
   */
  private static validateContext(context: SecurityContext, data: any): SecurityThreat[] {
    const threats: SecurityThreat[] = [];

    // Check for suspicious user agents
    if (this.isSuspiciousUserAgent(context.userAgent)) {
      threats.push({
        type: 'suspicious_user_agent',
        severity: 'medium',
        description: 'Suspicious user agent detected'
      });
    }

    // Check for unusual request patterns
    if (this.isUnusualRequestPattern(context, data)) {
      threats.push({
        type: 'unusual_pattern',
        severity: 'medium',
        description: 'Unusual request pattern detected'
      });
    }

    // Check for privilege escalation attempts
    if (this.isPrivilegeEscalationAttempt(context, data)) {
      threats.push({
        type: 'privilege_escalation',
        severity: 'critical',
        description: 'Potential privilege escalation attempt'
      });
    }

    return threats;
  }

  /**
   * Build security context from request
   */
  private static async buildSecurityContext(request: NextRequest): Promise<SecurityContext> {
    const session = await getServerSession(authOptions);
    
    return {
      isAuthenticated: !!session?.user,
      userId: session?.user?.id || undefined,
      userRole: (session?.user as any)?.role || 'user',
      ipAddress: this.getClientIP(request),
      userAgent: request.headers.get('user-agent') || 'unknown',
      requestPath: new URL(request.url).pathname,
      method: request.method
    };
  }

  /**
   * Sanitize data based on detected threats
   */
  private static sanitizeData(data: any, threats: SecurityThreat[]): any {
    if (typeof data === 'string') {
      let sanitized = data;
      
      // Remove potential XSS
      sanitized = sanitized.replace(/<script[^>]*>.*?<\/script>/gi, '');
      sanitized = sanitized.replace(/<iframe[^>]*>.*?<\/iframe>/gi, '');
      sanitized = sanitized.replace(/javascript:/gi, '');
      sanitized = sanitized.replace(/on\w+\s*=/gi, '');
      
      // Escape HTML entities
      sanitized = sanitized
        .replace(/&/g, '&amp;')
        .replace(/</g, '&lt;')
        .replace(/>/g, '&gt;')
        .replace(/"/g, '&quot;')
        .replace(/'/g, '&#x27;');
      
      return sanitized;
    }
    
    if (Array.isArray(data)) {
      return data.map(item => this.sanitizeData(item, threats));
    }
    
    if (typeof data === 'object' && data !== null) {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(data)) {
        sanitized[key] = this.sanitizeData(value, threats);
      }
      return sanitized;
    }
    
    return data;
  }

  /**
   * Helper methods
   */
  private static flattenToString(data: any): string {
    if (typeof data === 'string') return data;
    if (typeof data === 'number' || typeof data === 'boolean') return String(data);
    if (Array.isArray(data)) return data.map(item => this.flattenToString(item)).join(' ');
    if (typeof data === 'object' && data !== null) {
      return Object.values(data).map(value => this.flattenToString(value)).join(' ');
    }
    return '';
  }

  private static getThreatScore(severity: string): number {
    switch (severity) {
      case 'critical': return 50;
      case 'high': return 30;
      case 'medium': return 15;
      case 'low': return 5;
      default: return 0;
    }
  }

  private static getClientIP(request: NextRequest): string {
    return request.headers.get('x-forwarded-for')?.split(',')[0].trim() ||
           request.headers.get('x-real-ip') ||
           request.headers.get('cf-connecting-ip') ||
           '127.0.0.1';
  }

  private static isSuspiciousUserAgent(userAgent: string): boolean {
    const suspiciousPatterns = [
      /bot|crawler|spider|scraper/i,
      /curl|wget|python|java|go-http/i,
      /sqlmap|nikto|nmap|masscan/i
    ];
    
    return suspiciousPatterns.some(pattern => pattern.test(userAgent));
  }

  private static isUnusualRequestPattern(context: SecurityContext, data: any): boolean {
    // Check for unusually large payloads
    const dataSize = JSON.stringify(data).length;
    if (dataSize > 100000) return true; // 100KB limit
    
    // Check for unusual paths
    if (context.requestPath.includes('..') || context.requestPath.includes('%2e')) return true;
    
    return false;
  }

  private static isPrivilegeEscalationAttempt(context: SecurityContext, data: any): boolean {
    const dataString = this.flattenToString(data);
    
    // Check for admin-related keywords in non-admin contexts
    if (context.userRole !== 'admin' && /admin|root|superuser|privilege/i.test(dataString)) {
      return true;
    }
    
    return false;
  }

  private static checkForAnomalies(dataString: string, threats: SecurityThreat[]): void {
    // Check for excessive special characters
    const specialCharCount = (dataString.match(/[^a-zA-Z0-9\s]/g) || []).length;
    if (specialCharCount > dataString.length * 0.3) {
      threats.push({
        type: 'excessive_special_chars',
        severity: 'medium',
        description: 'Excessive special characters detected'
      });
    }
    
    // Check for base64 encoded content (potential payload)
    if (/^[A-Za-z0-9+/]+=*$/.test(dataString) && dataString.length > 50) {
      threats.push({
        type: 'base64_payload',
        severity: 'medium',
        description: 'Potential base64 encoded payload detected'
      });
    }
  }
}

export default ComprehensiveSecurityValidator;
