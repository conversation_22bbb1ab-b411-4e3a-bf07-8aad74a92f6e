/**
 * Enhanced Cache Key Generator
 * Provides collision-resistant, hierarchical cache key generation with intelligent invalidation
 */

import crypto from 'crypto';

interface CacheKeyConfig {
  namespace: string;
  version: string;
  userIsolation: boolean;
  sessionIsolation: boolean;
  compressionEnabled: boolean;
  ttlVariation: boolean;
}

interface CacheKeyMetadata {
  key: string;
  namespace: string;
  tags: string[];
  dependencies: string[];
  userSpecific: boolean;
  sessionSpecific: boolean;
  createdAt: number;
  expiresAt: number;
}

export class EnhancedCacheKeyGenerator {
  private static instance: EnhancedCacheKeyGenerator;
  private keyMetadata = new Map<string, CacheKeyMetadata>();
  private namespaceConfigs = new Map<string, CacheKeyConfig>();

  private readonly DEFAULT_CONFIG: CacheKeyConfig = {
    namespace: 'default',
    version: 'v1',
    userIsolation: true,
    sessionIsolation: false,
    compressionEnabled: false,
    ttlVariation: false
  };

  static getInstance(): EnhancedCacheKeyGenerator {
    if (!EnhancedCacheKeyGenerator.instance) {
      EnhancedCacheKeyGenerator.instance = new EnhancedCacheKeyGenerator();
    }
    return EnhancedCacheKeyGenerator.instance;
  }

  constructor() {
    this.initializeNamespaceConfigs();
  }

  /**
   * Initialize namespace-specific configurations
   */
  private initializeNamespaceConfigs(): void {
    // Assessment-related caching
    this.namespaceConfigs.set('assessment', {
      namespace: 'assessment',
      version: 'v2',
      userIsolation: true,
      sessionIsolation: true,
      compressionEnabled: true,
      ttlVariation: true
    });

    // Career data caching (static data)
    this.namespaceConfigs.set('career', {
      namespace: 'career',
      version: 'v1',
      userIsolation: false,
      sessionIsolation: false,
      compressionEnabled: true,
      ttlVariation: false
    });

    // User-specific data
    this.namespaceConfigs.set('user', {
      namespace: 'user',
      version: 'v1',
      userIsolation: true,
      sessionIsolation: false,
      compressionEnabled: false,
      ttlVariation: true
    });

    // Learning resources
    this.namespaceConfigs.set('learning', {
      namespace: 'learning',
      version: 'v1',
      userIsolation: false,
      sessionIsolation: false,
      compressionEnabled: true,
      ttlVariation: false
    });

    // AI service responses
    this.namespaceConfigs.set('ai', {
      namespace: 'ai',
      version: 'v1',
      userIsolation: true,
      sessionIsolation: true,
      compressionEnabled: true,
      ttlVariation: true
    });
  }

  /**
   * Generate collision-resistant cache key with metadata
   */
  generateKey(
    namespace: string,
    operation: string,
    params: Record<string, any> = {},
    userId?: string,
    sessionId?: string
  ): string {
    const config = this.namespaceConfigs.get(namespace) || this.DEFAULT_CONFIG;
    
    // Build key components
    const components: string[] = [
      config.namespace,
      config.version,
      operation
    ];

    // Add user isolation if enabled
    if (config.userIsolation && userId) {
      components.push(`user:${this.hashValue(userId)}`);
    }

    // Add session isolation if enabled
    if (config.sessionIsolation && sessionId) {
      components.push(`session:${this.hashValue(sessionId)}`);
    }

    // Process parameters
    const paramString = this.serializeParams(params);
    if (paramString) {
      components.push(`params:${this.hashValue(paramString)}`);
    }

    // Generate final key
    const baseKey = components.join(':');
    const finalKey = config.compressionEnabled ? 
      this.compressKey(baseKey) : 
      baseKey;

    // Store metadata
    this.storeKeyMetadata(finalKey, namespace, config, userId, sessionId);

    return finalKey;
  }

  /**
   * Generate hierarchical cache keys for related data
   */
  generateHierarchicalKey(
    namespace: string,
    hierarchy: string[],
    params: Record<string, any> = {},
    userId?: string,
    sessionId?: string
  ): string {
    const operation = hierarchy.join('.');
    return this.generateKey(namespace, operation, params, userId, sessionId);
  }

  /**
   * Generate batch cache keys for multiple operations
   */
  generateBatchKeys(
    namespace: string,
    operations: Array<{
      operation: string;
      params?: Record<string, any>;
      userId?: string;
      sessionId?: string;
    }>
  ): string[] {
    return operations.map(op => 
      this.generateKey(namespace, op.operation, op.params, op.userId, op.sessionId)
    );
  }

  /**
   * Generate pattern-based keys for bulk operations
   */
  generatePatternKey(
    namespace: string,
    pattern: string,
    variables: Record<string, string>,
    userId?: string,
    sessionId?: string
  ): string {
    let operation = pattern;
    for (const [key, value] of Object.entries(variables)) {
      operation = operation.replace(`{${key}}`, value);
    }
    
    return this.generateKey(namespace, operation, {}, userId, sessionId);
  }

  /**
   * Get keys by namespace for bulk invalidation
   */
  getKeysByNamespace(namespace: string): string[] {
    const keys: string[] = [];
    this.keyMetadata.forEach((metadata, key) => {
      if (metadata.namespace === namespace) {
        keys.push(key);
      }
    });
    return keys;
  }

  /**
   * Get keys by user for user-specific invalidation
   */
  getKeysByUser(userId: string): string[] {
    const userHash = this.hashValue(userId);
    const keys: string[] = [];

    this.keyMetadata.forEach((metadata, key) => {
      if (metadata.userSpecific && key.includes(`user:${userHash}`)) {
        keys.push(key);
      }
    });
    return keys;
  }

  /**
   * Get keys by tags for tag-based invalidation
   */
  getKeysByTags(tags: string[]): string[] {
    const keys: string[] = [];
    this.keyMetadata.forEach((metadata, key) => {
      const hasMatchingTag = metadata.tags.some(tag => tags.includes(tag));
      if (hasMatchingTag) {
        keys.push(key);
      }
    });
    return keys;
  }

  /**
   * Get expired keys for cleanup
   */
  getExpiredKeys(): string[] {
    const now = Date.now();
    const expiredKeys: string[] = [];

    this.keyMetadata.forEach((metadata, key) => {
      if (metadata.expiresAt && metadata.expiresAt < now) {
        expiredKeys.push(key);
      }
    });

    return expiredKeys;
  }

  /**
   * Clean up expired key metadata
   */
  cleanupExpiredMetadata(): void {
    const expiredKeys = this.getExpiredKeys();
    for (const key of expiredKeys) {
      this.keyMetadata.delete(key);
    }
  }

  /**
   * Serialize parameters for consistent key generation
   */
  private serializeParams(params: Record<string, any>): string {
    if (!params || Object.keys(params).length === 0) {
      return '';
    }

    // Sort keys for consistent ordering
    const sortedKeys = Object.keys(params).sort();
    const serialized = sortedKeys.map(key => {
      const value = params[key];
      if (value === null || value === undefined) {
        return `${key}:null`;
      }
      if (typeof value === 'object') {
        return `${key}:${JSON.stringify(value)}`;
      }
      return `${key}:${String(value)}`;
    }).join('|');

    return serialized;
  }

  /**
   * Hash value for collision resistance
   */
  private hashValue(value: string): string {
    return crypto
      .createHash('sha256')
      .update(value)
      .digest('hex')
      .substring(0, 16); // Use first 16 characters for brevity
  }

  /**
   * Compress key for storage efficiency
   */
  private compressKey(key: string): string {
    // Use base64 encoding of hash for compression
    const hash = crypto
      .createHash('sha256')
      .update(key)
      .digest('base64')
      .replace(/[+/=]/g, '') // Remove special characters
      .substring(0, 20); // Use first 20 characters
    
    return `compressed:${hash}`;
  }

  /**
   * Store key metadata for management
   */
  private storeKeyMetadata(
    key: string,
    namespace: string,
    config: CacheKeyConfig,
    userId?: string,
    sessionId?: string
  ): void {
    const metadata: CacheKeyMetadata = {
      key,
      namespace,
      tags: [namespace],
      dependencies: [],
      userSpecific: config.userIsolation && !!userId,
      sessionSpecific: config.sessionIsolation && !!sessionId,
      createdAt: Date.now(),
      expiresAt: 0 // Will be set when TTL is known
    };

    this.keyMetadata.set(key, metadata);
  }

  /**
   * Update key expiration time
   */
  updateKeyExpiration(key: string, ttlMs: number): void {
    const metadata = this.keyMetadata.get(key);
    if (metadata) {
      metadata.expiresAt = Date.now() + ttlMs;
      this.keyMetadata.set(key, metadata);
    }
  }

  /**
   * Add tags to existing key
   */
  addKeyTags(key: string, tags: string[]): void {
    const metadata = this.keyMetadata.get(key);
    if (metadata) {
      metadata.tags.push(...tags);
      this.keyMetadata.set(key, metadata);
    }
  }

  /**
   * Add dependencies to existing key
   */
  addKeyDependencies(key: string, dependencies: string[]): void {
    const metadata = this.keyMetadata.get(key);
    if (metadata) {
      metadata.dependencies.push(...dependencies);
      this.keyMetadata.set(key, metadata);
    }
  }

  /**
   * Get key metadata
   */
  getKeyMetadata(key: string): CacheKeyMetadata | undefined {
    return this.keyMetadata.get(key);
  }

  /**
   * Get all key metadata for debugging
   */
  getAllKeyMetadata(): Map<string, CacheKeyMetadata> {
    return new Map(this.keyMetadata);
  }

  /**
   * Clear all metadata
   */
  clearMetadata(): void {
    this.keyMetadata.clear();
  }
}

export const enhancedCacheKeyGenerator = EnhancedCacheKeyGenerator.getInstance();
