import { NextRequest } from 'next/server';
import { generateCSRFToken, getCSRFToken, validateCSRFToken } from '../csrf';

// Mock the auth module
jest.mock('../auth', () => ({
  authOptions: {}
}));

// Mock getServerSession
jest.mock('next-auth/next', () => ({
  getServerSession: jest.fn()
}));

const { getServerSession } = require('next-auth/next');

describe('CSRF Security Improvements', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Clear global CSRF tokens
    if (globalThis.__csrfTokens) {
      globalThis.__csrfTokens.clear();
    }
  });

  test('generateCSRFToken should create secure tokens with multiple entropy sources', () => {
    const token1 = generateCSRFToken();
    const token2 = generateCSRFToken();
    
    // Tokens should be different
    expect(token1).not.toBe(token2);
    
    // Tokens should have the expected format: uuid-timestamp-randomhex
    const tokenPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}-[0-9a-z]+-[0-9a-f]{32}$/;
    expect(token1).toMatch(tokenPattern);
    expect(token2).toMatch(tokenPattern);
    
    // Tokens should be sufficiently long for security
    expect(token1.length).toBeGreaterThan(70);
  });

  test('guest users should get tokens based on secure fingerprinting, not just IP', async () => {
    getServerSession.mockResolvedValue(null); // No authenticated user

    // Create two requests with same IP but different user agents
    const request1 = new NextRequest('http://localhost:3000/test', {
      headers: {
        'x-forwarded-for': '***********',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'accept-language': 'en-US,en;q=0.9',
        'accept-encoding': 'gzip, deflate, br'
      }
    });

    const request2 = new NextRequest('http://localhost:3000/test', {
      headers: {
        'x-forwarded-for': '***********', // Same IP
        'user-agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36', // Different UA
        'accept-language': 'en-US,en;q=0.9',
        'accept-encoding': 'gzip, deflate, br'
      }
    });

    const token1 = await getCSRFToken(request1);
    const token2 = await getCSRFToken(request2);

    // Different fingerprints should result in different tokens
    expect(token1).not.toBe(token2);
  });

  test('same guest fingerprint should get same token within expiry', async () => {
    getServerSession.mockResolvedValue(null);

    const headers = {
      'x-forwarded-for': '***********',
      'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
      'accept-language': 'en-US,en;q=0.9',
      'accept-encoding': 'gzip, deflate, br'
    };

    const request1 = new NextRequest('http://localhost:3000/test', { headers });
    const request2 = new NextRequest('http://localhost:3000/test', { headers });

    const token1 = await getCSRFToken(request1);
    const token2 = await getCSRFToken(request2);

    // Same fingerprint should get same token
    expect(token1).toBe(token2);
  });

  test('CSRF validation should work with secure fingerprinting', async () => {
    getServerSession.mockResolvedValue(null);

    const request = new NextRequest('http://localhost:3000/test', {
      headers: {
        'x-forwarded-for': '***********',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'accept-language': 'en-US,en;q=0.9',
        'accept-encoding': 'gzip, deflate, br'
      }
    });

    // Get a token
    const token = await getCSRFToken(request);

    // Validation should succeed with same request fingerprint
    const isValid = await validateCSRFToken(request, token);
    expect(isValid).toBe(true);

    // Validation should fail with different fingerprint
    const differentRequest = new NextRequest('http://localhost:3000/test', {
      headers: {
        'x-forwarded-for': '***********',
        'user-agent': 'Different User Agent', // Different fingerprint
        'accept-language': 'en-US,en;q=0.9',
        'accept-encoding': 'gzip, deflate, br'
      }
    });

    const isValidDifferent = await validateCSRFToken(differentRequest, token);
    expect(isValidDifferent).toBe(false);
  });

  test('authenticated users should use user ID for token generation', async () => {
    const mockSession = {
      user: { id: 'user123', email: '<EMAIL>' }
    };
    getServerSession.mockResolvedValue(mockSession);

    const request = new NextRequest('http://localhost:3000/test', {
      headers: {
        'x-forwarded-for': '***********',
        'user-agent': 'Mozilla/5.0'
      }
    });

    const token = await getCSRFToken(request);
    const isValid = await validateCSRFToken(request, token);

    expect(isValid).toBe(true);
  });
});
