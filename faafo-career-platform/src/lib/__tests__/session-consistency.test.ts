import { authOptions } from '../auth';

describe('Session Management Consistency', () => {
  test('session and JWT maxAge should be consistent', () => {
    const sessionMaxAge = authOptions.session?.maxAge;
    const jwtMaxAge = authOptions.jwt?.maxAge;
    
    expect(sessionMaxAge).toBeDefined();
    expect(jwtMaxAge).toBeDefined();
    expect(sessionMaxAge).toBe(jwtMaxAge);
    expect(sessionMaxAge).toBe(30 * 24 * 60 * 60); // 30 days
  });

  test('cookie maxAge should match session maxAge', () => {
    const sessionMaxAge = authOptions.session?.maxAge;
    const cookieMaxAge = authOptions.cookies?.sessionToken?.options?.maxAge;
    
    expect(cookieMaxAge).toBeDefined();
    expect(cookieMaxAge).toBe(sessionMaxAge);
  });

  test('session configuration should be properly structured', () => {
    expect(authOptions.session?.strategy).toBe('jwt');
    expect(authOptions.session?.updateAge).toBe(24 * 60 * 60); // 24 hours
  });

  test('cookie security settings should be appropriate', () => {
    const sessionCookie = authOptions.cookies?.sessionToken?.options;
    
    expect(sessionCookie?.httpOnly).toBe(true);
    expect(sessionCookie?.sameSite).toBe('lax');
    expect(sessionCookie?.path).toBe('/');
    
    // In development, secure should be false; in production, true
    if (process.env.NODE_ENV === 'production') {
      expect(sessionCookie?.secure).toBe(true);
    } else {
      expect(sessionCookie?.secure).toBe(false);
    }
  });
});
