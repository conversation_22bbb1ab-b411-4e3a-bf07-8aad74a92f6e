// Test development rate limiting configuration without importing the full module
// to avoid Jest issues with next-auth dependencies

// Mock the rate limit configurations directly
const rateLimitConfigs = {
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 5,
    message: 'Too many authentication attempts. Please try again later.'
  },
  api: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 100,
    message: 'Too many API requests. Please try again later.'
  },
  contact: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 3,
    message: 'Too many contact form submissions. Please try again later.'
  },
  signup: {
    windowMs: 60 * 60 * 1000, // 1 hour
    maxRequests: 5,
    message: 'Too many signup attempts. Please try again later.'
  }
};

const devRateLimitConfigs = {
  auth: {
    windowMs: 5 * 60 * 1000, // 5 minutes (shorter)
    maxRequests: 10, // Double the requests
    message: 'Too many authentication attempts. Please try again later. (Dev Mode)'
  },
  api: {
    windowMs: 5 * 60 * 1000, // 5 minutes (shorter)
    maxRequests: 200, // Double the requests
    message: 'Too many API requests. Please try again later. (Dev Mode)'
  },
  contact: {
    windowMs: 30 * 60 * 1000, // 30 minutes (shorter)
    maxRequests: 6, // Double the requests
    message: 'Too many contact form submissions. Please try again later. (Dev Mode)'
  },
  signup: {
    windowMs: 30 * 60 * 1000, // 30 minutes (shorter)
    maxRequests: 10, // Double the requests
    message: 'Too many signup attempts. Please try again later. (Dev Mode)'
  }
};

// Mock the getRateLimitConfig function
function getRateLimitConfig(type: keyof typeof rateLimitConfigs) {
  const enableDevRateLimit = process.env.ENABLE_DEV_RATE_LIMIT === 'true';
  const isDevelopment = process.env.NODE_ENV === 'development';

  if (isDevelopment && enableDevRateLimit) {
    return devRateLimitConfigs[type];
  }

  return rateLimitConfigs[type];
}

describe('Development Rate Limiting Configuration', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.clearAllMocks();
  });

  afterEach(() => {
    // Restore original environment
    Object.keys(process.env).forEach(key => {
      if (!(key in originalEnv)) {
        delete process.env[key];
      }
    });
    Object.assign(process.env, originalEnv);
  });

  describe('Configuration helpers', () => {
    test('should return dev config when in development with rate limiting enabled', () => {
      Object.assign(process.env, { NODE_ENV: 'development', ENABLE_DEV_RATE_LIMIT: 'true' });

      const config = getRateLimitConfig('auth');

      expect(config).toEqual(devRateLimitConfigs.auth);
      expect(config.maxRequests).toBe(10); // Dev config has double the requests
      expect(config.windowMs).toBe(5 * 60 * 1000); // Dev config has shorter window
    });

    test('should return production config when in production', () => {
      Object.assign(process.env, { NODE_ENV: 'production' });

      const config = getRateLimitConfig('auth');

      expect(config).toEqual(rateLimitConfigs.auth);
      expect(config.maxRequests).toBe(5); // Production config
      expect(config.windowMs).toBe(15 * 60 * 1000); // Production config
    });

    test('should return production config when in development with rate limiting disabled', () => {
      Object.assign(process.env, { NODE_ENV: 'development', ENABLE_DEV_RATE_LIMIT: 'false' });

      const config = getRateLimitConfig('auth');

      expect(config).toEqual(rateLimitConfigs.auth);
    });
  });

  describe('Development configuration validation', () => {
    test('dev configs should have more lenient limits than production', () => {
      const configTypes: (keyof typeof rateLimitConfigs)[] = ['auth', 'api', 'contact', 'signup'];

      configTypes.forEach(type => {
        const prodConfig = rateLimitConfigs[type];
        const devConfig = devRateLimitConfigs[type];

        expect(devConfig.maxRequests).toBeGreaterThan(prodConfig.maxRequests);
        expect(devConfig.windowMs).toBeLessThanOrEqual(prodConfig.windowMs);
        expect(devConfig.message).toContain('(Dev Mode)');
      });
    });
  });
});
