/**
 * React Hook for Skill Gap Analyzer Feature Flags
 * Provides easy access to feature flags in React components
 */

import { useState, useEffect, useCallback } from 'react';
import { useSession } from 'next-auth/react';
import { SkillGapFeatureFlags } from '@/lib/feature-flags/skill-gap-feature-flags';

interface FeatureFlagState {
  // Core features
  isSkillGapAnalyzerEnabled: boolean;
  isComprehensiveAnalysisEnabled: boolean;
  isAIRecommendationsEnabled: boolean;
  isMarketDataEnabled: boolean;
  
  // UI variants
  skillAssessmentUIVariant: string;
  analysisAlgorithmVariant: string;
  recommendationEngineVariant: string;
  
  // Performance features
  isCachingOptimizationEnabled: boolean;
  isParallelProcessingEnabled: boolean;
  isAdvancedAIModelsEnabled: boolean;
  
  // UX features
  isEnhancedUIEnabled: boolean;
  isInteractiveTutorialsEnabled: boolean;
  isRealTimeCollaborationEnabled: boolean;
  
  // Beta/Enterprise features
  isBetaFeaturesEnabled: boolean;
  isEnterpriseAnalyticsEnabled: boolean;
  
  // Loading state
  isLoading: boolean;
  error: string | null;
}

interface UseSkillGapFeatureFlagsOptions {
  organizationId?: string;
  enableAutoRefresh?: boolean;
  refreshInterval?: number; // milliseconds
}

export function useSkillGapFeatureFlags(options: UseSkillGapFeatureFlagsOptions = {}) {
  const { data: session } = useSession();
  const userId = session?.user?.id;
  
  const [flags, setFlags] = useState<FeatureFlagState>({
    // Default values (safe defaults)
    isSkillGapAnalyzerEnabled: false,
    isComprehensiveAnalysisEnabled: false,
    isAIRecommendationsEnabled: true,
    isMarketDataEnabled: false,
    skillAssessmentUIVariant: 'control',
    analysisAlgorithmVariant: 'standard',
    recommendationEngineVariant: 'basic',
    isCachingOptimizationEnabled: false,
    isParallelProcessingEnabled: false,
    isAdvancedAIModelsEnabled: false,
    isEnhancedUIEnabled: false,
    isInteractiveTutorialsEnabled: true,
    isRealTimeCollaborationEnabled: false,
    isBetaFeaturesEnabled: false,
    isEnterpriseAnalyticsEnabled: false,
    isLoading: true,
    error: null,
  });

  const featureFlags = new SkillGapFeatureFlags();

  const loadFeatureFlags = useCallback(async () => {
    if (!userId) {
      setFlags(prev => ({ ...prev, isLoading: false }));
      return;
    }

    try {
      setFlags(prev => ({ ...prev, isLoading: true, error: null }));

      // Update user context if organization is provided
      if (options.organizationId) {
        await featureFlags.updateUserContext(userId, {
          organizationId: options.organizationId,
        });
      }

      // Load all feature flags in parallel
      const [
        isSkillGapAnalyzerEnabled,
        isComprehensiveAnalysisEnabled,
        isAIRecommendationsEnabled,
        isMarketDataEnabled,
        skillAssessmentUIVariant,
        analysisAlgorithmVariant,
        recommendationEngineVariant,
        isCachingOptimizationEnabled,
        isParallelProcessingEnabled,
        isAdvancedAIModelsEnabled,
        isEnhancedUIEnabled,
        isInteractiveTutorialsEnabled,
        isRealTimeCollaborationEnabled,
        isBetaFeaturesEnabled,
        isEnterpriseAnalyticsEnabled,
      ] = await Promise.all([
        featureFlags.isSkillGapAnalyzerEnabled(userId),
        featureFlags.isComprehensiveAnalysisEnabled(userId),
        featureFlags.isAIRecommendationsEnabled(userId),
        featureFlags.isMarketDataEnabled(userId),
        featureFlags.getSkillAssessmentUIVariant(userId),
        featureFlags.getAnalysisAlgorithmVariant(userId),
        featureFlags.getRecommendationEngineVariant(userId),
        featureFlags.isCachingOptimizationEnabled(userId),
        featureFlags.isParallelProcessingEnabled(userId),
        featureFlags.isAdvancedAIModelsEnabled(userId),
        featureFlags.isEnhancedUIEnabled(userId),
        featureFlags.isInteractiveTutorialsEnabled(userId),
        featureFlags.isRealTimeCollaborationEnabled(userId),
        featureFlags.isBetaFeaturesEnabled(userId),
        featureFlags.isEnterpriseAnalyticsEnabled(userId, options.organizationId),
      ]);

      setFlags({
        isSkillGapAnalyzerEnabled,
        isComprehensiveAnalysisEnabled,
        isAIRecommendationsEnabled,
        isMarketDataEnabled,
        skillAssessmentUIVariant,
        analysisAlgorithmVariant,
        recommendationEngineVariant,
        isCachingOptimizationEnabled,
        isParallelProcessingEnabled,
        isAdvancedAIModelsEnabled,
        isEnhancedUIEnabled,
        isInteractiveTutorialsEnabled,
        isRealTimeCollaborationEnabled,
        isBetaFeaturesEnabled,
        isEnterpriseAnalyticsEnabled,
        isLoading: false,
        error: null,
      });

    } catch (error) {
      console.error('Error loading feature flags:', error);
      setFlags(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Failed to load feature flags',
      }));
    }
  }, [userId, options.organizationId]);

  // Load flags on mount and when dependencies change
  useEffect(() => {
    loadFeatureFlags();
  }, [loadFeatureFlags]);

  // Auto-refresh flags if enabled
  useEffect(() => {
    if (!options.enableAutoRefresh || !userId) return;

    const interval = setInterval(
      loadFeatureFlags,
      options.refreshInterval || 300000 // Default 5 minutes
    );

    return () => clearInterval(interval);
  }, [loadFeatureFlags, options.enableAutoRefresh, options.refreshInterval, userId]);

  // Track flag usage
  const trackFlagUsage = useCallback(async (
    flagName: string,
    value: any,
    context?: Record<string, any>
  ) => {
    if (!userId) return;

    try {
      await featureFlags.trackFlagUsage(userId, flagName, value, {
        ...context,
        source: 'react_hook',
        timestamp: Date.now(),
      });
    } catch (error) {
      console.error('Error tracking flag usage:', error);
    }
  }, [userId]);

  // Check specific feature with tracking
  const checkFeature = useCallback(async (flagName: string): Promise<boolean> => {
    if (!userId) return false;

    try {
      const isEnabled = await featureFlags.isSkillGapAnalyzerEnabled(userId);
      await trackFlagUsage(flagName, isEnabled, { method: 'checkFeature' });
      return isEnabled;
    } catch (error) {
      console.error(`Error checking feature ${flagName}:`, error);
      return false;
    }
  }, [userId, trackFlagUsage]);

  // Get A/B test variant with tracking
  const getVariant = useCallback(async (testName: string): Promise<string> => {
    if (!userId) return 'control';

    try {
      let variant = 'control';
      
      switch (testName) {
        case 'skill_assessment_ui':
          variant = await featureFlags.getSkillAssessmentUIVariant(userId);
          break;
        case 'analysis_algorithm':
          variant = await featureFlags.getAnalysisAlgorithmVariant(userId);
          break;
        case 'recommendation_engine':
          variant = await featureFlags.getRecommendationEngineVariant(userId);
          break;
        default:
          console.warn(`Unknown A/B test: ${testName}`);
      }

      await trackFlagUsage(`ab_test:${testName}`, variant, { method: 'getVariant' });
      return variant;
    } catch (error) {
      console.error(`Error getting variant for ${testName}:`, error);
      return 'control';
    }
  }, [userId, trackFlagUsage]);

  // Refresh flags manually
  const refreshFlags = useCallback(() => {
    return loadFeatureFlags();
  }, [loadFeatureFlags]);

  // Emergency disable feature
  const emergencyDisable = useCallback(async (flagName: string) => {
    try {
      await featureFlags.emergencyDisableFeature(flagName);
      await refreshFlags(); // Reload flags after emergency disable
    } catch (error) {
      console.error(`Error emergency disabling ${flagName}:`, error);
    }
  }, [refreshFlags]);

  return {
    // Flag states
    ...flags,
    
    // Helper methods
    checkFeature,
    getVariant,
    trackFlagUsage,
    refreshFlags,
    emergencyDisable,
    
    // Convenience getters for common patterns
    shouldShowFeature: (flagName: keyof FeatureFlagState) => {
      const value = flags[flagName];
      if (typeof value === 'boolean') {
        return value;
      }
      return false;
    },
    
    isVariant: (testName: string, expectedVariant: string) => {
      switch (testName) {
        case 'skill_assessment_ui':
          return flags.skillAssessmentUIVariant === expectedVariant;
        case 'analysis_algorithm':
          return flags.analysisAlgorithmVariant === expectedVariant;
        case 'recommendation_engine':
          return flags.recommendationEngineVariant === expectedVariant;
        default:
          return false;
      }
    },
    
    // Feature availability checks
    canUseAdvancedFeatures: flags.isBetaFeaturesEnabled || flags.isEnterpriseAnalyticsEnabled,
    canUseAIFeatures: flags.isAIRecommendationsEnabled && !flags.error,
    canUseEnterpriseFeatures: flags.isEnterpriseAnalyticsEnabled,
  };
}

// Higher-order component for feature flag protection
export function withFeatureFlag<P extends object>(
  WrappedComponent: React.ComponentType<P>,
  flagName: keyof FeatureFlagState,
  fallbackComponent?: React.ComponentType<P>
) {
  return function FeatureFlagWrapper(props: P) {
    const { shouldShowFeature, isLoading } = useSkillGapFeatureFlags();

    if (isLoading) {
      return <div>Loading...</div>;
    }

    if (!shouldShowFeature(flagName)) {
      if (fallbackComponent) {
        const FallbackComponent = fallbackComponent;
        return <FallbackComponent {...props} />;
      }
      return null;
    }

    return <WrappedComponent {...props} />;
  };
}

// Component for conditional rendering based on feature flags
interface FeatureFlagProps {
  flag: keyof FeatureFlagState;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function FeatureFlag({ flag, children, fallback = null }: FeatureFlagProps) {
  const { shouldShowFeature, isLoading } = useSkillGapFeatureFlags();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return shouldShowFeature(flag) ? <>{children}</> : <>{fallback}</>;
}

// Component for A/B test variant rendering
interface ABTestProps {
  test: string;
  variants: Record<string, React.ReactNode>;
  fallback?: React.ReactNode;
}

export function ABTest({ test, variants, fallback = null }: ABTestProps) {
  const { isVariant, isLoading } = useSkillGapFeatureFlags();

  if (isLoading) {
    return <div>Loading...</div>;
  }

  for (const [variant, content] of Object.entries(variants)) {
    if (isVariant(test, variant)) {
      return <>{content}</>;
    }
  }

  return <>{fallback}</>;
}
