/**
 * Enhanced Assessment Logic Test Suite
 * Tests the improved assessment scoring algorithms and career recommendation systems
 */

import { describe, it, expect, beforeAll, jest } from '@jest/globals';
import { AlgorithmicAssessmentService } from '../../lib/algorithmicAssessmentService';
import { EnhancedFallbackService } from '../../lib/enhancedFallbackService';
import { AssessmentServiceIntegration } from '../../lib/assessmentServiceIntegration';
import { generateAssessmentInsights, AssessmentResponse } from '../../lib/assessmentScoring';

// Mock data for testing
const mockAssessmentResponse: AssessmentResponse = {
  current_role: 'Software Developer',
  years_experience: '3-5',
  skill_development_interest: ['coding_tech', 'ai_ml', 'mobile_development'],
  career_values: ['growth', 'autonomy', 'impact'],
  work_style_preferences: ['remote', 'collaborative', 'flexible'],
  biggest_obstacles: ['skill_gaps', 'time_constraints'],
  financial_readiness: 4,
  support_level: 3,
  risk_tolerance: 3,
  urgency_level: 4,
  skills_confidence: 75,
  desired_outcomes_work_life: 'Better work-life balance',
  desired_outcomes_financial: 'Higher salary',
  desired_outcomes_personal: 'Career growth',
  location: 'San Francisco, CA'
};

const mockInsights = {
  scores: {
    financialReadiness: 4,
    supportLevel: 3,
    riskTolerance: 3,
    urgencyLevel: 4,
    skillsConfidence: 75,
    readinessScore: 70
  },
  primaryMotivation: 'Career advancement',
  topSkills: ['technical_programming', 'data_analysis', 'project_management'],
  biggestObstacles: ['skill_gaps', 'time_constraints'],
  recommendedTimeline: '6-12 months',
  keyRecommendations: ['Focus on AI/ML skills', 'Build portfolio projects'],
  careerPathSuggestions: [],
  careerPathAnalysis: [],
  overallSkillGaps: [],
  learningPriorities: ['technical_programming', 'data_analysis', 'project_management'],
  estimatedTransitionTime: '6-12 months'
};

describe('Enhanced Assessment Logic', () => {
  beforeAll(async () => {
    // Initialize the assessment services
    await AlgorithmicAssessmentService.initialize();
    await AssessmentServiceIntegration.initialize();
  });

  describe('AlgorithmicAssessmentService', () => {
    it('should generate career recommendations with algorithmic matching', async () => {
      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(
        mockAssessmentResponse,
        mockInsights
      );

      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBeGreaterThan(0);
      
      // Check structure of first recommendation
      const firstRec = recommendations[0];
      expect(firstRec).toHaveProperty('careerPath');
      expect(firstRec).toHaveProperty('matchScore');
      expect(firstRec).toHaveProperty('matchFactors');
      expect(firstRec).toHaveProperty('confidenceLevel');
      expect(firstRec).toHaveProperty('reasoning');
      expect(firstRec).toHaveProperty('skillGaps');
      expect(firstRec).toHaveProperty('estimatedTimeline');
      expect(firstRec).toHaveProperty('successProbability');
      
      // Validate match score range
      expect(firstRec.matchScore).toBeGreaterThanOrEqual(0);
      expect(firstRec.matchScore).toBeLessThanOrEqual(100);
      
      // Validate confidence level
      expect(firstRec.confidenceLevel).toBeGreaterThanOrEqual(0);
      expect(firstRec.confidenceLevel).toBeLessThanOrEqual(100);
      
      // Validate success probability
      expect(firstRec.successProbability).toBeGreaterThanOrEqual(0);
      expect(firstRec.successProbability).toBeLessThanOrEqual(100);
    });

    it('should calculate skill alignment correctly', async () => {
      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(
        mockAssessmentResponse,
        mockInsights
      );

      const techRecommendation = recommendations.find(rec => 
        rec.careerPath.name.toLowerCase().includes('developer') ||
        rec.careerPath.name.toLowerCase().includes('engineer')
      );

      if (techRecommendation) {
        expect(techRecommendation.matchFactors.skillAlignment).toBeGreaterThan(50);
      }
    });

    it('should provide detailed skill gap analysis', async () => {
      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(
        mockAssessmentResponse,
        mockInsights
      );

      const firstRec = recommendations[0];
      expect(Array.isArray(firstRec.skillGaps)).toBe(true);
      
      if (firstRec.skillGaps.length > 0) {
        const skillGap = firstRec.skillGaps[0];
        expect(skillGap).toHaveProperty('skill');
        expect(skillGap).toHaveProperty('currentLevel');
        expect(skillGap).toHaveProperty('requiredLevel');
        expect(skillGap).toHaveProperty('priority');
        expect(skillGap).toHaveProperty('estimatedLearningTime');
        
        expect(['HIGH', 'MEDIUM', 'LOW']).toContain(skillGap.priority);
      }
    });

    it('should generate meaningful reasoning', async () => {
      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(
        mockAssessmentResponse,
        mockInsights
      );

      const firstRec = recommendations[0];
      expect(Array.isArray(firstRec.reasoning)).toBe(true);
      expect(firstRec.reasoning.length).toBeGreaterThan(0);
      expect(firstRec.reasoning[0]).toBeTruthy();
      expect(typeof firstRec.reasoning[0]).toBe('string');
    });

    it('should handle edge cases gracefully', async () => {
      const emptyResponse: AssessmentResponse = {
        current_role: '',
        years_experience: '',
        skill_development_interest: [],
        career_values: [],
        work_style_preferences: [],
        biggest_obstacles: [],
        financial_readiness: 1,
        support_level: 1,
        risk_tolerance: 1,
        urgency_level: 1,
        skills_confidence: 10,
        desired_outcomes_work_life: '',
        desired_outcomes_financial: '',
        desired_outcomes_personal: '',
        location: ''
      };

      const emptyInsights = {
        ...mockInsights,
        topSkills: [],
        scores: {
          financialReadiness: 1,
          supportLevel: 1,
          riskTolerance: 1,
          urgencyLevel: 1,
          skillsConfidence: 10,
          readinessScore: 20
        }
      };

      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(
        emptyResponse,
        emptyInsights
      );

      expect(recommendations).toBeDefined();
      expect(Array.isArray(recommendations)).toBe(true);
      // Should still provide some recommendations even with minimal data
    });
  });

  describe('EnhancedFallbackService', () => {
    it('should generate comprehensive fallback insights', async () => {
      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(
        mockAssessmentResponse,
        mockInsights,
        'Testing fallback service'
      );

      expect(fallbackInsights).toBeDefined();
      expect(fallbackInsights).toHaveProperty('careerRecommendations');
      expect(fallbackInsights).toHaveProperty('skillGapAnalysis');
      expect(fallbackInsights).toHaveProperty('learningPathRecommendations');
      expect(fallbackInsights).toHaveProperty('marketInsights');
      expect(fallbackInsights).toHaveProperty('personalizedAdvice');
      expect(fallbackInsights).toHaveProperty('confidenceScore');
      expect(fallbackInsights).toHaveProperty('fallbackReason');
      expect(fallbackInsights).toHaveProperty('generatedAt');
    });

    it('should provide quality career recommendations in fallback mode', async () => {
      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(
        mockAssessmentResponse,
        mockInsights
      );

      const recommendations = fallbackInsights.careerRecommendations;
      expect(Array.isArray(recommendations)).toBe(true);
      expect(recommendations.length).toBeGreaterThan(0);
      
      const firstRec = recommendations[0];
      expect(firstRec).toHaveProperty('careerPath');
      expect(firstRec).toHaveProperty('matchPercentage');
      expect(firstRec).toHaveProperty('reasoning');
      expect(firstRec).toHaveProperty('salaryRange');
      expect(firstRec).toHaveProperty('marketOutlook');
      expect(firstRec).toHaveProperty('transitionDifficulty');
      expect(firstRec).toHaveProperty('timeToTransition');
      expect(firstRec).toHaveProperty('keySkillsNeeded');
      expect(firstRec).toHaveProperty('successFactors');
      expect(firstRec).toHaveProperty('potentialChallenges');
      
      expect(['LOW', 'MEDIUM', 'HIGH']).toContain(firstRec.transitionDifficulty);
    });

    it('should provide actionable skill gap analysis', async () => {
      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(
        mockAssessmentResponse,
        mockInsights
      );

      const skillGaps = fallbackInsights.skillGapAnalysis;
      expect(Array.isArray(skillGaps)).toBe(true);
      
      if (skillGaps.length > 0) {
        const skillGap = skillGaps[0];
        expect(skillGap).toHaveProperty('skill');
        expect(skillGap).toHaveProperty('currentLevel');
        expect(skillGap).toHaveProperty('targetLevel');
        expect(skillGap).toHaveProperty('priority');
        expect(skillGap).toHaveProperty('learningTime');
        expect(skillGap).toHaveProperty('recommendedApproach');
        expect(skillGap).toHaveProperty('marketValue');
        
        expect(['HIGH', 'MEDIUM', 'LOW']).toContain(skillGap.priority);
        expect(skillGap.marketValue).toBeGreaterThanOrEqual(1);
        expect(skillGap.marketValue).toBeLessThanOrEqual(10);
      }
    });

    it('should generate personalized advice', async () => {
      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(
        mockAssessmentResponse,
        mockInsights
      );

      const advice = fallbackInsights.personalizedAdvice;
      expect(Array.isArray(advice)).toBe(true);
      expect(advice.length).toBeGreaterThan(0);
      
      advice.forEach(adviceItem => {
        expect(typeof adviceItem).toBe('string');
        expect(adviceItem.length).toBeGreaterThan(10); // Should be meaningful advice
      });
    });

    it('should provide market insights', async () => {
      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(
        mockAssessmentResponse,
        mockInsights
      );

      const marketInsights = fallbackInsights.marketInsights;
      expect(marketInsights).toHaveProperty('industryTrends');
      expect(marketInsights).toHaveProperty('emergingSkills');
      expect(marketInsights).toHaveProperty('salaryTrends');
      expect(marketInsights).toHaveProperty('jobMarketHealth');
      expect(marketInsights).toHaveProperty('geographicOpportunities');
      expect(marketInsights).toHaveProperty('automationImpact');
      
      expect(['EXCELLENT', 'GOOD', 'FAIR', 'CHALLENGING']).toContain(marketInsights.jobMarketHealth);
      expect(Array.isArray(marketInsights.industryTrends)).toBe(true);
      expect(Array.isArray(marketInsights.emergingSkills)).toBe(true);
      expect(Array.isArray(marketInsights.geographicOpportunities)).toBe(true);
    });

    it('should calculate appropriate confidence scores', async () => {
      const fallbackInsights = await EnhancedFallbackService.generateFallbackInsights(
        mockAssessmentResponse,
        mockInsights
      );

      expect(fallbackInsights.confidenceScore).toBeGreaterThanOrEqual(60);
      expect(fallbackInsights.confidenceScore).toBeLessThanOrEqual(95);
    });
  });

  describe('AssessmentServiceIntegration', () => {
    it('should provide integrated career recommendations', async () => {
      const result = await AssessmentServiceIntegration.generateCareerRecommendations(
        mockAssessmentResponse,
        mockInsights
      );

      expect(result).toBeDefined();
      expect(result).toHaveProperty('careerRecommendations');
      expect(result).toHaveProperty('serviceUsed');
      expect(result).toHaveProperty('confidence');
      expect(result).toHaveProperty('processingTime');

      expect(Array.isArray(result.careerRecommendations)).toBe(true);
      expect(result.careerRecommendations.length).toBeGreaterThan(0);
      expect(['algorithmic', 'fallback', 'basic']).toContain(result.serviceUsed);
      expect(result.confidence).toBeGreaterThanOrEqual(0);
      expect(result.confidence).toBeLessThanOrEqual(100);
      expect(result.processingTime).toBeGreaterThan(0);
    });

    it('should perform health checks correctly', async () => {
      const healthCheck = await AssessmentServiceIntegration.healthCheck();

      expect(healthCheck).toBeDefined();
      expect(healthCheck).toHaveProperty('algorithmic');
      expect(healthCheck).toHaveProperty('fallback');
      expect(healthCheck).toHaveProperty('basic');
      expect(healthCheck).toHaveProperty('overall');

      expect(typeof healthCheck.algorithmic).toBe('boolean');
      expect(typeof healthCheck.fallback).toBe('boolean');
      expect(typeof healthCheck.basic).toBe('boolean');
      expect(['healthy', 'degraded', 'critical']).toContain(healthCheck.overall);
    });

    it('should handle service failures gracefully', async () => {
      // Test with invalid data to trigger fallbacks
      const invalidResponse: AssessmentResponse = {
        current_role: null as any,
        years_experience: null as any,
        skill_development_interest: null as any,
        career_values: null as any,
        work_style_preferences: null as any,
        biggest_obstacles: null as any,
        financial_readiness: null as any,
        support_level: null as any,
        risk_tolerance: null as any,
        urgency_level: null as any,
        skills_confidence: null as any,
        desired_outcomes_work_life: null as any,
        desired_outcomes_financial: null as any,
        desired_outcomes_personal: null as any,
        location: null as any
      };

      const result = await AssessmentServiceIntegration.generateCareerRecommendations(
        invalidResponse,
        mockInsights
      );

      // Should still provide some recommendations even with invalid data
      expect(result).toBeDefined();
      expect(Array.isArray(result.careerRecommendations)).toBe(true);
      expect(result.careerRecommendations.length).toBeGreaterThan(0);
    });
  });

  describe('Integration with Assessment Scoring', () => {
    it('should integrate algorithmic recommendations with assessment insights', async () => {
      const insights = await generateAssessmentInsights(mockAssessmentResponse);
      
      expect(insights).toHaveProperty('careerPathSuggestions');
      expect(Array.isArray(insights.careerPathSuggestions)).toBe(true);
      expect(insights.careerPathSuggestions.length).toBeGreaterThan(0);
      
      // Should not contain hardcoded default suggestions when algorithmic service works
      const hasOnlyDefaults = insights.careerPathSuggestions.every(suggestion =>
        ['Digital Marketing Specialist', 'Entrepreneur / Startup Founder', 'Full-Stack Web Developer'].includes(suggestion)
      );
      
      // With proper algorithmic matching, we should get more diverse suggestions
      expect(hasOnlyDefaults).toBe(false);
    });

    it('should handle fallback gracefully when algorithmic service fails', async () => {
      // Mock the algorithmic service to throw an error
      const originalImport = jest.requireActual('../../lib/algorithmicAssessmentService');
      jest.doMock('../../lib/algorithmicAssessmentService', () => ({
        AlgorithmicAssessmentService: {
          generateCareerRecommendations: jest.fn().mockRejectedValue(new Error('Service unavailable'))
        }
      }));

      const insights = await generateAssessmentInsights(mockAssessmentResponse);
      
      expect(insights).toHaveProperty('careerPathSuggestions');
      expect(Array.isArray(insights.careerPathSuggestions)).toBe(true);
      expect(insights.careerPathSuggestions.length).toBeGreaterThan(0);
      
      // Should still provide meaningful suggestions via fallback
      expect(insights.careerPathSuggestions).not.toEqual([]);
    });
  });

  describe('Performance and Reliability', () => {
    it('should complete assessment generation within reasonable time', async () => {
      const startTime = Date.now();
      
      const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(
        mockAssessmentResponse,
        mockInsights
      );
      
      const endTime = Date.now();
      const duration = endTime - startTime;
      
      expect(duration).toBeLessThan(5000); // Should complete within 5 seconds
      expect(recommendations.length).toBeGreaterThan(0);
    });

    it('should handle concurrent requests properly', async () => {
      const promises = Array(5).fill(null).map(() =>
        AlgorithmicAssessmentService.generateCareerRecommendations(
          mockAssessmentResponse,
          mockInsights
        )
      );

      const results = await Promise.all(promises);
      
      results.forEach(result => {
        expect(Array.isArray(result)).toBe(true);
        expect(result.length).toBeGreaterThan(0);
      });
    });

    it('should maintain consistency across multiple calls', async () => {
      const result1 = await AlgorithmicAssessmentService.generateCareerRecommendations(
        mockAssessmentResponse,
        mockInsights
      );
      
      const result2 = await AlgorithmicAssessmentService.generateCareerRecommendations(
        mockAssessmentResponse,
        mockInsights
      );

      // Results should be consistent for the same input
      expect(result1.length).toBe(result2.length);
      expect(result1[0].careerPath.name).toBe(result2[0].careerPath.name);
      expect(result1[0].matchScore).toBe(result2[0].matchScore);
    });
  });
});
