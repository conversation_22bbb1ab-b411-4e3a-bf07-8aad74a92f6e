import { NextRequest } from 'next/server';
import { middleware } from '../../middleware';

// Mock getToken from next-auth/jwt
jest.mock('next-auth/jwt', () => ({
  getToken: jest.fn()
}));

const { getToken } = require('next-auth/jwt');

describe('Authentication Redirect Logic', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Mock console.error to avoid noise in tests
    jest.spyOn(console, 'error').mockImplementation(() => {});
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('should redirect unauthenticated users from protected routes to login', async () => {
    getToken.mockResolvedValue(null); // No token (unauthenticated)

    const request = new NextRequest('http://localhost:3000/dashboard');
    const response = await middleware(request);

    expect(response.status).toBe(307); // Next.js uses 307 for redirects
    expect(response.headers.get('location')).toContain('/login');
    expect(response.headers.get('location')).toContain('callbackUrl=%2Fdashboard');
  });

  test('should redirect authenticated users from auth pages to dashboard', async () => {
    getToken.mockResolvedValue({ sub: 'user123' }); // Valid token

    const request = new NextRequest('http://localhost:3000/login');
    const response = await middleware(request);

    expect(response.status).toBe(307); // Next.js uses 307 for redirects
    expect(response.headers.get('location')).toContain('/dashboard');
  });

  test('should prevent infinite redirect loops with redirect count tracking', async () => {
    getToken.mockResolvedValue(null); // Unauthenticated

    // Simulate a request that's already been redirected multiple times
    const request = new NextRequest('http://localhost:3000/dashboard', {
      headers: {
        'x-middleware-redirect-count': '4' // Already redirected 4 times
      }
    });

    const response = await middleware(request);

    expect(response.status).toBe(500);
    expect(await response.json()).toEqual({
      error: 'Authentication redirect loop detected'
    });
  });

  test('should handle callback URL redirects correctly for authenticated users', async () => {
    getToken.mockResolvedValue({ sub: 'user123' }); // Valid token

    const request = new NextRequest('http://localhost:3000/login?callbackUrl=%2Fprofile');
    const response = await middleware(request);

    expect(response.status).toBe(307); // Next.js uses 307 for redirects
    expect(response.headers.get('location')).toContain('/profile');
  });

  test('should prevent redirect loops for authenticated users on auth pages', async () => {
    getToken.mockResolvedValue({ sub: 'user123' }); // Valid token

    // Simulate multiple redirects
    const request = new NextRequest('http://localhost:3000/login', {
      headers: {
        'x-middleware-redirect-count': '4'
      }
    });

    const response = await middleware(request);

    // Should allow access to prevent infinite loops
    expect(response.status).toBe(200);
    expect(response.headers.get('x-frame-options')).toBe('DENY'); // Security headers should be applied
  });

  test('should increment redirect count on each redirect', async () => {
    getToken.mockResolvedValue(null); // Unauthenticated

    const request = new NextRequest('http://localhost:3000/dashboard', {
      headers: {
        'x-middleware-redirect-count': '2'
      }
    });

    const response = await middleware(request);

    expect(response.status).toBe(307); // Next.js uses 307 for redirects
    expect(response.headers.get('x-middleware-redirect-count')).toBe('3');
  });

  test('should handle protected API routes correctly', async () => {
    getToken.mockResolvedValue(null); // Unauthenticated

    const request = new NextRequest('http://localhost:3000/api/profile');
    const response = await middleware(request);

    expect(response.status).toBe(401);
    expect(await response.json()).toEqual({
      error: 'Authentication required'
    });
  });

  test('should allow access to public API routes', async () => {
    getToken.mockResolvedValue(null); // Unauthenticated

    const request = new NextRequest('http://localhost:3000/api/career-paths');
    const response = await middleware(request);

    // Should pass through to next middleware/handler
    expect(response.status).toBe(200);
    expect(response.headers.get('x-frame-options')).toBe('DENY'); // Security headers applied
  });

  test('should skip middleware for static files', async () => {
    const request = new NextRequest('http://localhost:3000/_next/static/css/app.css');
    const response = await middleware(request);

    // Should pass through without any processing
    expect(response.status).toBe(200);
    expect(response.headers.get('x-frame-options')).toBeNull(); // No security headers for static files
  });

  test('should handle complex redirect scenarios without loops', async () => {
    getToken.mockResolvedValue(null); // Unauthenticated

    // Test multiple protected route access attempts
    const routes = ['/dashboard', '/profile', '/assessment'];
    
    for (const route of routes) {
      const request = new NextRequest(`http://localhost:3000${route}`);
      const response = await middleware(request);
      
      expect(response.status).toBe(307); // Next.js uses 307 for redirects
      expect(response.headers.get('location')).toContain('/login');
      expect(response.headers.get('location')).toContain(`callbackUrl=${encodeURIComponent(route)}`);
    }
  });

  test('should preserve query parameters in callback URL', async () => {
    getToken.mockResolvedValue(null); // Unauthenticated

    const request = new NextRequest('http://localhost:3000/dashboard?tab=analytics&filter=recent');
    const response = await middleware(request);

    expect(response.status).toBe(307); // Next.js uses 307 for redirects
    const location = response.headers.get('location');
    expect(location).toContain('/login');
    expect(location).toContain('callbackUrl=%2Fdashboard%3Ftab%3Danalytics%26filter%3Drecent');
  });
});
