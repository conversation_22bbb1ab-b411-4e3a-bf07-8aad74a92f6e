import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';

interface AchievementsResponse {
  achievements: any[];
  total: number;
  unlocked?: number;
}

interface UserAchievementResponse {
  id: string;
  userId: string;
  achievementId: string;
  progress?: number;
  unlockedAt: Date;
  achievement: any;
}
// GET handler to retrieve user achievements
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const error = new Error('Unauthorized') as any;
    error.statusCode = 401;
    throw error;
  }

  const { searchParams } = new URL(request.url);
  const type = searchParams.get('type');
  const includeAll = searchParams.get('includeAll') === 'true';

  if (includeAll) {
    // Get all achievements with user's unlock status
    const allAchievements = await prisma.achievement.findMany({
      where: { isActive: true },
      include: {
        userAchievements: {
          where: { userId: session.user.id },
        },
      },
      orderBy: { createdAt: 'asc' },
    });

    const achievementsWithStatus = allAchievements.map(achievement => ({
      ...achievement,
      isUnlocked: achievement.userAchievements.length > 0,
      unlockedAt: achievement.userAchievements[0]?.unlockedAt || null,
      progress: achievement.userAchievements[0]?.progress || null,
    }));

    return NextResponse.json({
      success: true,
      data: {
        achievements: achievementsWithStatus,
        total: allAchievements.length,
        unlocked: achievementsWithStatus.filter(a => a.isUnlocked).length,
      }
    });
  } else {
    // Get only user's unlocked achievements
    const whereClause: any = {
      userId: session.user.id,
    };

    const userAchievements = await prisma.userAchievement.findMany({
      where: whereClause,
      include: {
        achievement: true,
      },
      orderBy: { unlockedAt: 'desc' },
    });

    const achievements = userAchievements
      .filter(ua => ua.achievement && (!type || ua.achievement.type === type))
      .map(ua => ({
        ...ua.achievement,
        unlockedAt: ua.unlockedAt,
        progress: ua.progress,
      }));

    return NextResponse.json({
      success: true,
      data: {
        achievements,
        total: achievements.length,
      }
    });
  }
});

// POST handler to unlock an achievement for a user
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }

        const body = await request.json();
        const { achievementId, progress } = body;

        if (!achievementId) {
          const error = new Error('Achievement ID is required') as any;
          error.statusCode = 400;
          throw error;
        }

        // Check if achievement exists
        const achievement = await prisma.achievement.findUnique({
          where: { id: achievementId, isActive: true },
        });

        if (!achievement) {
          const error = new Error('Achievement not found') as any;
          error.statusCode = 404;
          throw error;
        }

        // Check if user already has this achievement
        const existingUserAchievement = await prisma.userAchievement.findUnique({
          where: {
            userId_achievementId: {
              userId: session.user.id,
              achievementId,
            },
          },
        });

        if (existingUserAchievement) {
          const error = new Error('Achievement already unlocked') as any;
          error.statusCode = 400;
          throw error;
        }

        // Create user achievement
        const userAchievement = await prisma.userAchievement.create({
          data: {
            userId: session.user.id,
            achievementId,
            progress,
          },
          include: {
            achievement: true,
          },
        });

        return NextResponse.json({
          success: true,
          data: userAchievement
        }, { status: 201 });
      }
    );
  });
});

// PUT handler to update achievement progress
export const PUT = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 30 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }

        const body = await request.json();
        const { achievementId, progress } = body;

        if (!achievementId) {
          const error = new Error('Achievement ID is required') as any;
          error.statusCode = 400;
          throw error;
        }

        // Check if user has this achievement
        const userAchievement = await prisma.userAchievement.findUnique({
          where: {
            userId_achievementId: {
              userId: session.user.id,
              achievementId,
            },
          },
        });

        if (!userAchievement) {
          const error = new Error('User achievement not found') as any;
          error.statusCode = 404;
          throw error;
        }

        // Update progress
        const updatedUserAchievement = await prisma.userAchievement.update({
          where: {
            userId_achievementId: {
              userId: session.user.id,
              achievementId,
            },
          },
          data: { progress },
          include: {
            achievement: true,
          },
        });

        return NextResponse.json({
          success: true,
          data: updatedUserAchievement
        });
      }
    );
  });
});
