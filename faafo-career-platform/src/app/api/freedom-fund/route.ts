import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { Prisma } from '@prisma/client';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface FreedomFundSaveRequest {
  monthlyExpenses: number;
  coverageMonths: number;
  currentSavingsAmount?: number;
  monthlyContribution?: number;
  adjustForInflation?: boolean;
}

interface FreedomFundUpdateRequest {
  monthlyExpenses?: number;
  coverageMonths?: number;
  currentSavingsAmount?: number;
  monthlyContribution?: number;
  adjustForInflation?: boolean;
}

// POST handler to create or update FreedomFund data
export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session || !session.user || !session.user.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }
        const body = await request.json() as FreedomFundSaveRequest;
        const { monthlyExpenses, coverageMonths, currentSavingsAmount, monthlyContribution, adjustForInflation } = body;

        // Enhanced validation
        if (typeof monthlyExpenses !== 'number' || monthlyExpenses <= 0) {
          const error = new Error('Monthly expenses must be a positive number.') as any;
          error.statusCode = 400;
          throw error;
        }

        if (typeof coverageMonths !== 'number' || ![3, 6, 9, 12].includes(coverageMonths)) {
          const error = new Error('Coverage months must be 3, 6, 9, or 12.') as any;
          error.statusCode = 400;
          throw error;
        }

        if (currentSavingsAmount !== undefined && (typeof currentSavingsAmount !== 'number' || currentSavingsAmount < 0)) {
          const error = new Error('Current savings amount must be a non-negative number.') as any;
          error.statusCode = 400;
          throw error;
        }

        if (monthlyContribution !== undefined && (typeof monthlyContribution !== 'number' || monthlyContribution < 0)) {
          const error = new Error('Monthly contribution must be a non-negative number.') as any;
          error.statusCode = 400;
          throw error;
        }

        // Calculate target savings with optional inflation adjustment
        const baseTarget = monthlyExpenses * coverageMonths;
        const inflationRate = 0.03; // 3% annual inflation
        const targetSavings = adjustForInflation ? baseTarget * (1 + inflationRate) : baseTarget;

        const freedomFundEntry = await prisma.freedomFund.upsert({
          where: { userId: session.user.id },
          update: {
            monthlyExpenses,
            coverageMonths,
            targetSavings,
            currentSavingsAmount: currentSavingsAmount === undefined ? null : currentSavingsAmount,
          },
          create: {
            userId: session.user.id,
            monthlyExpenses,
            coverageMonths,
            targetSavings,
            currentSavingsAmount: currentSavingsAmount === undefined ? null : currentSavingsAmount,
          },
        });

        return NextResponse.json({
          success: true,
          data: freedomFundEntry
        });

      }
    );
  }) as Promise<NextResponse<ApiResponse<any>>>;
});

// GET handler to retrieve FreedomFund data
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    const error = new Error('Unauthorized') as any;
    error.statusCode = 401;
    throw error;
  }

  const freedomFundEntry = await prisma.freedomFund.findUnique({
    where: { userId: session.user.id },
  });

  if (!freedomFundEntry) {
    const error = new Error('No Freedom Fund data found for this user.') as any;
    error.statusCode = 404;
    throw error;
  }

  return NextResponse.json({
    success: true,
    data: freedomFundEntry
  });
});

// PUT handler to update specific fields of FreedomFund data
export const PUT = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    const error = new Error('Unauthorized') as any;
    error.statusCode = 401;
    throw error;
  }

  const body = await request.json() as FreedomFundUpdateRequest;
  const { monthlyExpenses, coverageMonths, currentSavingsAmount, monthlyContribution, adjustForInflation } = body;

  // Check if the Freedom Fund entry exists
  const existingEntry = await prisma.freedomFund.findUnique({
    where: { userId: session.user.id },
  });

  if (!existingEntry) {
    const error = new Error('No Freedom Fund data found for this user. Please create one first.') as any;
    error.statusCode = 404;
    throw error;
  }

  // Validate provided fields
  if (monthlyExpenses !== undefined && (typeof monthlyExpenses !== 'number' || monthlyExpenses <= 0)) {
    const error = new Error('Monthly expenses must be a positive number.') as any;
    error.statusCode = 400;
    throw error;
  }

  if (coverageMonths !== undefined && (typeof coverageMonths !== 'number' || ![3, 6, 9, 12].includes(coverageMonths))) {
    const error = new Error('Coverage months must be 3, 6, 9, or 12.') as any;
    error.statusCode = 400;
    throw error;
  }

  if (currentSavingsAmount !== undefined && (typeof currentSavingsAmount !== 'number' || currentSavingsAmount < 0)) {
    const error = new Error('Current savings amount must be a non-negative number.') as any;
    error.statusCode = 400;
    throw error;
  }

  if (monthlyContribution !== undefined && (typeof monthlyContribution !== 'number' || monthlyContribution < 0)) {
    const error = new Error('Monthly contribution must be a non-negative number.') as any;
    error.statusCode = 400;
    throw error;
  }

  // Prepare update data
  const updateData: any = {};

  if (monthlyExpenses !== undefined) updateData.monthlyExpenses = monthlyExpenses;
  if (coverageMonths !== undefined) updateData.coverageMonths = coverageMonths;
  if (currentSavingsAmount !== undefined) updateData.currentSavingsAmount = currentSavingsAmount;

  // Recalculate target savings if monthly expenses or coverage months changed
  if (monthlyExpenses !== undefined || coverageMonths !== undefined) {
    const finalMonthlyExpenses = monthlyExpenses ?? existingEntry.monthlyExpenses;
    const finalCoverageMonths = coverageMonths ?? existingEntry.coverageMonths;
    const baseTarget = finalMonthlyExpenses * finalCoverageMonths;
    const inflationRate = 0.03;
    updateData.targetSavings = adjustForInflation ? baseTarget * (1 + inflationRate) : baseTarget;
  }

  const updatedEntry = await prisma.freedomFund.update({
    where: { userId: session.user.id },
    data: updateData,
  });

  return NextResponse.json({
    success: true,
    data: updatedEntry
  });

});

// DELETE handler to remove FreedomFund data
export const DELETE = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    const error = new Error('Unauthorized') as any;
    error.statusCode = 401;
    throw error;
  }

  const existingEntry = await prisma.freedomFund.findUnique({
    where: { userId: session.user.id },
  });

  if (!existingEntry) {
    const error = new Error('No Freedom Fund data found for this user.') as any;
    error.statusCode = 404;
    throw error;
  }

  await prisma.freedomFund.delete({
    where: { userId: session.user.id },
  });

  return NextResponse.json({
    success: true,
    data: { message: 'Freedom Fund data deleted successfully.' }
  });
});