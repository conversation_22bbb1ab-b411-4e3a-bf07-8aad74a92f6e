import { NextRequest, NextResponse } from 'next/server';
import { edgeCaseHandlerService } from '@/lib/skills/EdgeCaseHandlerService';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  const { testType, data } = await request.json();

  switch (testType) {
    case 'skill-assessment':
      return await testSkillAssessment(data);

    case 'learning-path':
      return await testLearningPath(data);

    case 'market-data':
      return await testMarketData(data);

    case 'error-statistics':
      return await testErrorStatistics();

    case 'health-status':
      return await testHealthStatus();

    default:
      const error = new Error('Invalid test type') as any;
      error.statusCode = 400;
      throw error;
  }
});

async function testSkillAssessment(data: any) {
  const result = await edgeCaseHandlerService.createSkillAssessmentWithDatabase({
    userId: data.userId || 'test-user-123',
    skillIds: data.skillIds || ['javascript', 'react'],
    careerPathId: data.careerPathId,
    assessmentType: data.assessmentType || 'comprehensive'
  });

  return NextResponse.json({
    success: true,
    data: {
      testType: 'skill-assessment',
      edgeCaseResult: result,
      message: 'Skill assessment test completed'
    }
  });
}

async function testLearningPath(data: any) {
  const result = await edgeCaseHandlerService.generateLearningPathWithDatabase({
    userId: data.userId || 'test-user-123',
    targetRole: data.targetRole || 'Full Stack Developer',
    currentSkills: data.currentSkills || [
      { skill: 'JavaScript', level: 6 },
      { skill: 'React', level: 5 }
    ],
    timeframe: data.timeframe || 6,
    budget: data.budget || 1000,
    availability: data.availability || 10
  });

  return NextResponse.json({
    success: true,
    data: {
      testType: 'learning-path',
      edgeCaseResult: result,
      message: 'Learning path test completed'
    }
  });
}

async function testMarketData(data: any) {
  const result = await edgeCaseHandlerService.getMarketDataWithDatabase({
    skill: data.skill || 'JavaScript',
    location: data.location || 'San Francisco',
    forceRefresh: data.forceRefresh || false
  });

  return NextResponse.json({
    success: true,
    testType: 'market-data',
    edgeCaseResult: result,
    message: 'Market data test completed'
  });
}

async function testErrorStatistics() {
  const stats = await edgeCaseHandlerService.getErrorStatistics();

  return NextResponse.json({
    success: true,
    testType: 'error-statistics',
    data: stats,
    message: 'Error statistics retrieved'
  });
}

async function testHealthStatus() {
  const health = await edgeCaseHandlerService.getHealthStatus();

  return NextResponse.json({
    success: true,
    testType: 'health-status',
    data: health,
    message: 'Health status retrieved'
  });
}

// GET endpoint for quick health check
export async function GET() {
  try {
    const health = await edgeCaseHandlerService.getHealthStatus();
    const stats = await edgeCaseHandlerService.getErrorStatistics();

    return NextResponse.json({
      success: true,
      message: 'EdgeCaseHandler service is operational',
      health,
      stats,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    return NextResponse.json(
      { 
        success: false, 
        error: 'EdgeCaseHandler service check failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
