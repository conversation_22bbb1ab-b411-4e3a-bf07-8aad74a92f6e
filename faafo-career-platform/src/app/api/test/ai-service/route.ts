/**
 * Test endpoint for AI service functionality
 * ONLY FOR TESTING - BYPASSES AUTHENTICATION
 */

import { NextRequest, NextResponse } from 'next/server';
import { geminiService } from '@/lib/services/geminiService';
import { aiServiceMonitor } from '@/lib/ai-service-monitor';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  const { testType, data } = await request.json();

  switch (testType) {
    case 'resume-analysis':
      return await testResumeAnalysis(data);

    case 'career-recommendations':
      return await testCareerRecommendations(data);

    case 'interview-questions':
      return await testInterviewQuestions(data);

    case 'health-check':
      return await testHealthCheck();

    case 'monitoring':
      return await testMonitoring();

    default:
      const error = new Error('Invalid test type') as any;
      error.statusCode = 400;
      throw error;
  }
});

async function testResumeAnalysis(data: any) {
  const resumeText = data.resumeText || `
John Doe
Software Engineer
Email: <EMAIL>

EXPERIENCE:
Senior Software Engineer at TechCorp (2020-2024)
- Led development of microservices architecture
- Implemented CI/CD pipelines
- Mentored junior developers

SKILLS:
JavaScript, React, Node.js, Python, AWS
`;

  const result = await geminiService.analyzeResume(resumeText, 'test-user-123');
  
  return NextResponse.json({
    success: true as const,
    data: {
      testType: 'resume-analysis',
      result
    }
  });
}

async function testCareerRecommendations(data: any) {
  const assessmentData = data.assessmentData || { experience: 'senior', interests: ['technology'] };
  const currentSkills = data.currentSkills || ['JavaScript', 'React', 'Node.js'];
  const preferences = data.preferences || { workStyle: 'collaborative' };

  const result = await geminiService.generateCareerRecommendations(
    assessmentData,
    currentSkills,
    preferences,
    'test-user-123'
  );
  
  return NextResponse.json({
    success: true as const,
    data: {
      testType: 'career-recommendations',
      result
    }
  });
}

async function testInterviewQuestions(data: any) {
  const params = {
    sessionType: 'TECHNICAL_PRACTICE',
    careerPath: 'Software Engineer',
    experienceLevel: 'SENIOR',
    difficulty: 'INTERMEDIATE',
    count: 3,
    ...data
  };

  const result = await geminiService.generateInterviewQuestions(params);
  
  return NextResponse.json({
    success: true as const,
    data: {
      testType: 'interview-questions',
      result
    }
  });
}

async function testHealthCheck() {
  const healthStatus = await geminiService.healthCheck();
  
  return NextResponse.json({
    success: true as const,
    data: {
      testType: 'health-check',
      result: healthStatus
    }
  });
}

async function testMonitoring() {
  const metrics = aiServiceMonitor.getMetrics();
  const analytics = aiServiceMonitor.getUsageAnalytics();
  const insights = aiServiceMonitor.getPerformanceInsights();
  
  return NextResponse.json({
    success: true as const,
    data: {
      testType: 'monitoring',
      result: {
        metrics,
        analytics,
        insights
      }
    }
  });
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  const { searchParams } = new URL(request.url);
  const testType = searchParams.get('test');

  if (!testType) {
    return NextResponse.json({
      success: true,
      data: {
        message: 'AI Service Test Endpoint',
        availableTests: [
          'resume-analysis',
          'career-recommendations',
          'interview-questions',
          'health-check',
          'monitoring'
        ],
        usage: {
          POST: 'Send { "testType": "test-name", "data": {...} }',
          GET: 'Use ?test=test-name for simple tests'
        }
      }
    });
  }

  // Simple GET tests
  switch (testType) {
    case 'health-check':
      return await testHealthCheck();

    case 'monitoring':
      return await testMonitoring();

    case 'resume-analysis':
      return await testResumeAnalysis({});

    default:
      const error = new Error('Invalid test type for GET request') as any;
      error.statusCode = 400;
      throw error;
  }
});
