import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

interface UserSearchResult {
  id: string;
  name: string | null;
  email: string;
  profile: {
    profilePictureUrl: string | null;
    bio: string | null;
    currentCareerPath: string | null;
    progressLevel: string | null;
  } | null;
}

interface UserSearchResponse {
  users: UserSearchResult[];
  total: number;
}

// GET /api/users/search - Search users for mentions
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<UserSearchResponse>>> => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const { searchParams } = new URL(request.url);
  const query = searchParams.get('q');
  const limit = parseInt(searchParams.get('limit') || '10');

  if (!query || query.length < 1) {
    return NextResponse.json({
      success: true,
      data: {
        users: [],
        total: 0,
      }
    });
  }

  // Search users by name, email, or profile information
  const users = await prisma.user.findMany({
    where: {
      AND: [
        {
          id: {
            not: session.user.id, // Exclude current user
          },
        },
        {
          OR: [
            {
              name: {
                contains: query,
                mode: 'insensitive',
              },
            },
            {
              email: {
                contains: query,
                mode: 'insensitive',
              },
            },
            {
              profile: {
                bio: {
                  contains: query,
                  mode: 'insensitive',
                },
              },
            },
          ],
        },
      ],
    },
    select: {
      id: true,
      name: true,
      email: true,
      profile: {
        select: {
          profilePictureUrl: true,
          bio: true,
          currentCareerPath: true,
          progressLevel: true,
        },
      },
    },
    take: Math.min(limit, 20), // Max 20 results
    orderBy: [
      {
        name: 'asc',
      },
      {
        email: 'asc',
      },
    ],
  });

  return NextResponse.json({
    success: true,
    data: {
      users,
      total: users.length,
    }
  });
});
