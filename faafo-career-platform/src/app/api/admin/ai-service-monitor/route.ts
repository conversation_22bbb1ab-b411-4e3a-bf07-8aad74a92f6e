/**
 * AI Service Monitoring Dashboard API
 * Provides real-time metrics, health status, and analytics for the AI service
 */

import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { aiServiceMonitor } from '@/lib/ai-service-monitor';
import { isUserAdmin } from '@/lib/auth-utils';
import { geminiService } from '@/lib/services/geminiService';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface MonitorResponse {
  timestamp: number;
  overview?: any;
  metrics?: any;
  analytics?: any;
  insights?: any;
  health?: any;
}

// GET /api/admin/ai-service-monitor
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  // Check authentication and admin privileges
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  // Check if user is admin using proper role-based authorization
  const isAdmin = await isUserAdmin(session.user.id);

  if (!isAdmin) {
    const error = new Error('Admin privileges required') as any;
    error.statusCode = 403;
    throw error;
  }

  const { searchParams } = new URL(request.url);
  const view = searchParams.get('view') || 'overview';

  switch (view) {
    case 'health':
      return await getHealthStatus();

    case 'metrics':
      return getMetrics();

    case 'analytics':
      return getAnalytics();

    case 'insights':
      return getInsights();

    case 'export':
      return exportMetrics();

    default:
      return getOverview();
  }
});

// POST /api/admin/ai-service-monitor (for actions like reset)
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const isAdmin = await isUserAdmin(session.user.id);

  if (!isAdmin) {
    const error = new Error('Admin privileges required') as any;
    error.statusCode = 403;
    throw error;
  }

  const { action } = await request.json();

  switch (action) {
    case 'reset':
      aiServiceMonitor.reset();
      return NextResponse.json({
        success: true,
        data: { success: true, message: 'Metrics reset successfully' }
      });

    case 'health-check':
      const healthStatus = await aiServiceMonitor.getHealthStatus();
      return NextResponse.json({
        success: true,
        data: { success: true, health: healthStatus }
      });

    default:
      const error = new Error('Invalid action') as any;
      error.statusCode = 400;
      throw error;
  }
});

async function getHealthStatus() {
  const healthStatus = await aiServiceMonitor.getHealthStatus();
  const geminiHealth = await geminiService.healthCheck();

  return NextResponse.json({
    success: true,
    data: {
      timestamp: Date.now(),
      overall: healthStatus,
      components: {
        ai: geminiHealth.ai,
        cache: geminiHealth.cache,
        monitor: healthStatus.status === 'healthy'
      }
    }
  });
}

function getMetrics() {
  const metrics = aiServiceMonitor.getMetrics();

  return NextResponse.json({
    success: true,
    data: {
      timestamp: Date.now(),
      metrics: {
        requests: {
          total: metrics.totalRequests,
          successful: metrics.successfulRequests,
          failed: metrics.failedRequests,
          successRate: metrics.totalRequests > 0 ?
            (metrics.successfulRequests / metrics.totalRequests) * 100 : 0
        },
        performance: {
          averageResponseTime: metrics.averageResponseTime,
          cacheHitRate: metrics.cacheHitRate,
          rateLimitHits: metrics.rateLimitHits
        },
        system: {
          uptime: metrics.uptime,
          lastHealthCheck: metrics.lastHealthCheck
        },
        recentActivity: metrics.recentPerformance,
        healthTrend: metrics.healthTrend
      }
    }
  });
}

function getAnalytics() {
  const analytics = aiServiceMonitor.getUsageAnalytics();

  return NextResponse.json({
    success: true,
    data: {
      timestamp: Date.now(),
      analytics: {
        usage: {
          dailyRequests: analytics.dailyRequests,
          peakHours: analytics.peakUsageHours,
          operationBreakdown: analytics.operationBreakdown
        },
        users: {
          activeUsers: Object.keys(analytics.userActivity).length,
          topUsers: Object.entries(analytics.userActivity)
            .sort(([,a], [,b]) => b - a)
            .slice(0, 10)
            .map(([userId, requests]) => ({ userId, requests }))
        },
        errors: {
          patterns: analytics.errorPatterns,
          totalErrors: Object.values(analytics.errorPatterns).reduce((sum, count) => sum + count, 0)
        }
      }
    }
  });
}

function getInsights() {
  const insights = aiServiceMonitor.getPerformanceInsights();

  return NextResponse.json({
    success: true,
    data: {
      timestamp: Date.now(),
      insights: {
        performance: {
          slowestOperations: insights.slowestOperations,
          errorRates: insights.errorRateByOperation,
          cacheEffectiveness: insights.cacheEffectiveness
        },
        recommendations: insights.recommendations,
        alerts: generateAlerts(insights)
      }
    }
  });
}

function exportMetrics() {
  const exportData = aiServiceMonitor.exportMetrics();
  
  return new NextResponse(exportData, {
    headers: {
      'Content-Type': 'application/json',
      'Content-Disposition': `attachment; filename="ai-service-metrics-${Date.now()}.json"`
    }
  });
}

function getOverview() {
  const metrics = aiServiceMonitor.getMetrics();
  const analytics = aiServiceMonitor.getUsageAnalytics();
  const insights = aiServiceMonitor.getPerformanceInsights();

  return NextResponse.json({
    success: true,
    data: {
      timestamp: Date.now(),
      overview: {
        status: metrics.healthTrend.length > 0 ?
          metrics.healthTrend[metrics.healthTrend.length - 1].status : 'unknown',
        totalRequests: metrics.totalRequests,
        successRate: metrics.totalRequests > 0 ?
          (metrics.successfulRequests / metrics.totalRequests) * 100 : 0,
        averageResponseTime: metrics.averageResponseTime,
        cacheHitRate: metrics.cacheHitRate,
        uptime: metrics.uptime,
        activeOperations: Object.keys(analytics.operationBreakdown).length,
        topRecommendations: insights.recommendations.slice(0, 3),
        recentErrors: Object.entries(analytics.errorPatterns)
          .sort(([,a], [,b]) => b - a)
          .slice(0, 5)
          .map(([error, count]) => ({ error, count }))
      }
    }
  });
}

function generateAlerts(insights: any): Array<{ level: 'info' | 'warning' | 'error'; message: string }> {
  const alerts: Array<{ level: 'info' | 'warning' | 'error'; message: string }> = [];
  
  // Check for performance issues
  if (insights.slowestOperations.length > 0 && insights.slowestOperations[0].avgResponseTime > 10000) {
    alerts.push({
      level: 'error',
      message: `Slow operation detected: ${insights.slowestOperations[0].operation} averaging ${Math.round(insights.slowestOperations[0].avgResponseTime)}ms`
    });
  }
  
  // Check for high error rates
  if (insights.errorRateByOperation.length > 0 && insights.errorRateByOperation[0].errorRate > 10) {
    alerts.push({
      level: 'warning',
      message: `High error rate: ${insights.errorRateByOperation[0].operation} has ${insights.errorRateByOperation[0].errorRate.toFixed(1)}% error rate`
    });
  }
  
  // Check cache effectiveness
  if (insights.cacheEffectiveness.hitRate < 20) {
    alerts.push({
      level: 'warning',
      message: `Low cache hit rate: ${insights.cacheEffectiveness.hitRate.toFixed(1)}% - consider optimizing caching strategy`
    });
  }
  
  // Check for no recent activity
  const metrics = aiServiceMonitor.getMetrics();
  if (metrics.recentPerformance.length === 0) {
    alerts.push({
      level: 'info',
      message: 'No recent AI service activity detected'
    });
  }
  
  return alerts;
}
