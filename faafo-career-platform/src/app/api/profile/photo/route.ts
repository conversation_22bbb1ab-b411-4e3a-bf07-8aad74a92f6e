import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import sharp from 'sharp';
import crypto from 'crypto';
import { put } from '@vercel/blob';
import { log } from '@/lib/logger';
import { withCSRFProtection } from '@/lib/csrf';

// File upload configuration
const MAX_FILE_SIZE = 5 * 1024 * 1024; // 5MB
const ALLOWED_TYPES = ['image/jpeg', 'image/png', 'image/webp'];
const AVATAR_SIZES = {
  thumbnail: 64,
  small: 128,
  medium: 256,
  large: 512
};

interface ProcessedImage {
  buffer: Buffer;
  size: number;
  format: string;
}

interface PhotoUploadResponse {
  success: true;
  profilePictureUrl: string;
  sizes: Record<string, string>;
  message: string;
}

interface PhotoDeleteResponse {
  success: true;
  message: string;
}

async function processImage(buffer: Buffer, size: number): Promise<ProcessedImage> {
  const processed = await sharp(buffer)
    .resize(size, size, {
      fit: 'cover',
      position: 'center'
    })
    .jpeg({ quality: 85 })
    .toBuffer();

  return {
    buffer: processed,
    size,
    format: 'jpeg'
  };
}

function generateFileName(userId: string, size: string): string {
  const timestamp = Date.now();
  const hash = crypto.createHash('md5').update(`${userId}-${timestamp}`).digest('hex').substring(0, 8);
  return `profile-${userId}-${size}-${hash}.jpg`;
}

// Upload file to Vercel Blob storage
async function uploadToStorage(buffer: Buffer, fileName: string): Promise<string> {
  try {
    // Check if BLOB_READ_WRITE_TOKEN is available
    if (!process.env.BLOB_READ_WRITE_TOKEN) {
      log.warn('BLOB_READ_WRITE_TOKEN not found, using local storage fallback', {
        component: 'photo_upload_api',
        action: 'upload_to_storage',
        metadata: { fallback: 'local_storage' }
      });
      // Fallback to local storage for development
      const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
      return `${baseUrl}/api/profile/photo/${fileName}`;
    }

    // Upload to Vercel Blob
    const blob = await put(`profile-photos/${fileName}`, buffer, {
      access: 'public',
      contentType: 'image/jpeg', // All images are converted to JPEG
    });

    return blob.url;
  } catch (error) {
    log.error('Error uploading to Vercel Blob', error as Error, {
      component: 'photo_upload_api',
      action: 'upload_to_storage',
      metadata: { fileName }
    });
    // Fallback to local storage
    const baseUrl = process.env.NEXTAUTH_URL || 'http://localhost:3000';
    return `${baseUrl}/api/profile/photo/${fileName}`;
  }
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      const error = new Error('Not authenticated') as any;
      error.statusCode = 401;
      throw error;
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      const error = new Error('User not found') as any;
      error.statusCode = 404;
      throw error;
    }

    const formData = await request.formData();
    const file = formData.get('file') as File;

    if (!file) {
      const error = new Error('No file provided') as any;
      error.statusCode = 400;
      throw error;
    }

    // Validate file type
    if (!ALLOWED_TYPES.includes(file.type)) {
      const error = new Error('Invalid file type. Only JPEG, PNG, and WebP are allowed.') as any;
      error.statusCode = 400;
      throw error;
    }

    // Validate file size
    if (file.size > MAX_FILE_SIZE) {
      const error = new Error('File too large. Maximum size is 5MB.') as any;
      error.statusCode = 400;
      throw error;
    }

    const buffer = Buffer.from(await file.arrayBuffer());

    // Process image for different sizes
    const processedImages = await Promise.all(
      Object.entries(AVATAR_SIZES).map(async ([sizeName, sizePixels]) => {
        const processed = await processImage(buffer, sizePixels);
        const fileName = generateFileName(user.id, sizeName);
        const url = await uploadToStorage(processed.buffer, fileName);
        return { size: sizeName, url, pixels: sizePixels };
      })
    );

    // Use the medium size as the primary profile picture URL
    const primaryImageUrl = processedImages.find(img => img.size === 'medium')?.url;

    if (!primaryImageUrl) {
      throw new Error('Failed to process primary image');
    }

    // Update user profile with new image URL
    const updatedProfile = await prisma.profile.upsert({
      where: { userId: user.id },
      update: {
        profilePictureUrl: primaryImageUrl,
        lastProfileUpdate: new Date(),
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        profilePictureUrl: primaryImageUrl,
        lastProfileUpdate: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        success: true,
        profilePictureUrl: primaryImageUrl,
        sizes: processedImages.reduce((acc, img) => {
          acc[img.size] = img.url;
          return acc;
        }, {} as Record<string, string>),
        message: 'Profile photo updated successfully'
      }
    });
  });
});

export const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    const session = await getServerSession(authOptions);

    if (!session?.user?.email) {
      const error = new Error('Not authenticated') as any;
      error.statusCode = 401;
      throw error;
    }

    const user = await prisma.user.findUnique({
      where: { email: session.user.email },
    });

    if (!user) {
      const error = new Error('User not found') as any;
      error.statusCode = 404;
      throw error;
    }

    // Remove profile picture URL from database
    await prisma.profile.upsert({
      where: { userId: user.id },
      update: {
        profilePictureUrl: null,
        lastProfileUpdate: new Date(),
        updatedAt: new Date(),
      },
      create: {
        userId: user.id,
        profilePictureUrl: null,
        lastProfileUpdate: new Date(),
      },
    });

    return NextResponse.json({
      success: true,
      data: {
        success: true,
        message: 'Profile photo removed successfully'
      }
    });
  });
});

// GET endpoint for serving local photos (fallback when Vercel Blob is not available)
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const { pathname } = new URL(request.url);
  const fileName = pathname.split('/').pop();

  if (!fileName) {
    const error = new Error('File not found') as any;
    error.statusCode = 404;
    throw error;
  }

  // In a real implementation, you would serve from local storage or redirect to CDN
  // For now, return a placeholder response indicating the feature needs Vercel Blob
  const error = new Error('Photo serving requires Vercel Blob storage configuration') as any;
  error.statusCode = 501;
  throw error;
});
