import { NextRequest, NextResponse } from 'next/server';
import { sendEmail } from '@/lib/email'; // Adjust path if necessary
import React from 'react'; // Import React
import { ContactFormEmail } from '@/emails/ContactFormEmail'; // Import the new email template
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface ContactResponse {
  message: string;
}

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 5 }, // 5 contact submissions per 15 minutes
      async () => {
        const { name, email, subject, message } = await request.json();

        if (!name || !email || !subject || !message) {
          const error = new Error('All fields are required') as any;
          error.statusCode = 400;
          throw error;
        }

        // Basic email format validation (more robust validation can be added)
        if (!/^[\w.-]+@[\w.-]+\.[a-zA-Z]{2,6}$/.test(email)) {
          const error = new Error('Invalid email address') as any;
          error.statusCode = 400;
          throw error;
        }

        await sendEmail({
          to: process.env.SUPPORT_EMAIL || '<EMAIL>', // Use an environment variable for support email
          subject: `Contact Form Submission: ${subject}`,
          template: React.createElement(ContactFormEmail, { name, email, subject, message }),
        });

        return NextResponse.json({
          success: true,
          data: { message: 'Your message has been sent successfully!' }
        }, { status: 200 });
      }
    );
  });
});