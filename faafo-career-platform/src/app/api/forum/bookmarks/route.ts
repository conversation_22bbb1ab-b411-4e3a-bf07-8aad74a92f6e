import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';

interface BookmarksResponse {
  bookmarks: Array<{
    id: string;
    createdAt: Date;
    post: any;
  }>;
  pagination: {
    page: number;
    limit: number;
    totalCount: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

interface BookmarkCreateResponse {
  message: string;
  bookmark: any;
}

interface BookmarkDeleteResponse {
  message: string;
}
// GET /api/forum/bookmarks - Get user's bookmarked posts
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<BookmarksResponse>>> => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = parseInt(searchParams.get('limit') || '10');
  const skip = (page - 1) * limit;

  // Get user's bookmarked posts
  const bookmarks = await prisma.forumBookmark.findMany({
    where: {
      userId: session.user.id,
    },
    include: {
      post: {
        include: {
          author: {
            select: {
              id: true,
              name: true,
              email: true,
              image: true,
            },
          },
          category: true,
          _count: {
            select: {
              replies: true,
              reactions: true,
            },
          },
        },
      },
    },
    orderBy: {
      createdAt: 'desc',
    },
    skip,
    take: limit,
  });

  // Get total count for pagination
  const totalCount = await prisma.forumBookmark.count({
    where: {
      userId: session.user.id,
    },
  });

  const totalPages = Math.ceil(totalCount / limit);

  return NextResponse.json({
    success: true,
    data: {
      bookmarks: bookmarks.map(bookmark => ({
        id: bookmark.id,
        createdAt: bookmark.createdAt,
        post: bookmark.post,
      })),
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      },
    }
  });
});

// POST /api/forum/bookmarks - Add bookmark
export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
          const error = new Error('Authentication required') as any;
          error.statusCode = 401;
          throw error;
        }

        const body = await request.json();
        const { postId } = body;

        if (!postId) {
          const error = new Error('Post ID is required') as any;
          error.statusCode = 400;
          throw error;
        }

        // Check if post exists
        const post = await prisma.forumPost.findUnique({
          where: { id: postId },
        });

        if (!post) {
          const error = new Error('Post not found') as any;
          error.statusCode = 404;
          throw error;
        }

        // Check if bookmark already exists
        const existingBookmark = await prisma.forumBookmark.findUnique({
          where: {
            userId_postId: {
              userId: session.user.id,
              postId,
            },
          },
        });

        if (existingBookmark) {
          const error = new Error('Post already bookmarked') as any;
          error.statusCode = 409;
          throw error;
        }

        // Create bookmark
        const bookmark = await prisma.forumBookmark.create({
          data: {
            userId: session.user.id,
            postId,
          },
          include: {
            post: {
              select: {
                id: true,
                title: true,
              },
            },
          },
        });

        return NextResponse.json({
          success: true,
          data: {
            message: 'Post bookmarked successfully',
            bookmark,
          }
        });
      }
    );
  });
});

// DELETE /api/forum/bookmarks - Remove bookmark
export const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session?.user?.id) {
          const error = new Error('Authentication required') as any;
          error.statusCode = 401;
          throw error;
        }

        const body = await request.json();
        const { postId, bookmarkId } = body;

        if (!postId && !bookmarkId) {
          const error = new Error('Post ID or Bookmark ID is required') as any;
          error.statusCode = 400;
          throw error;
        }

        let bookmark;

        if (bookmarkId) {
          // Delete by bookmark ID
          bookmark = await prisma.forumBookmark.findFirst({
            where: {
              id: bookmarkId,
              userId: session.user.id,
            },
          });
        } else {
          // Delete by post ID
          bookmark = await prisma.forumBookmark.findFirst({
            where: {
              postId,
              userId: session.user.id,
            },
          });
        }

        if (!bookmark) {
          const error = new Error('Bookmark not found') as any;
          error.statusCode = 404;
          throw error;
        }

        await prisma.forumBookmark.delete({
          where: { id: bookmark.id },
        });

        return NextResponse.json({
          success: true,
          data: {
            message: 'Bookmark removed successfully',
          }
        });
      }
    );
  });
});