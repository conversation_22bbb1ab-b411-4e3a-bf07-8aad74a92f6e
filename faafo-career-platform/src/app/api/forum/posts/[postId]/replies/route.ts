import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';

interface ForumReplyCreateResponse {
  id: string;
  content: string;
  authorId: string;
  postId: string;
  createdAt: Date;
  updatedAt: Date;
  author: any;
}
// POST handler to create a reply to a forum post
export const POST = withUnifiedErrorHandling(async (request: NextRequest, { params }: { params: Promise<{ postId: string }> }) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
        const { postId } = await params;
        const session = await getServerSession(authOptions);

        if (!session || !session.user || !session.user.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }

        const { content } = await request.json();

        if (!content) {
          const error = new Error('Content is required') as any;
          error.statusCode = 400;
          throw error;
        }

        if (content.length > 2000) {
          const error = new Error('Reply content must be 2000 characters or less') as any;
          error.statusCode = 400;
          throw error;
        }

        // Check if the post exists
        const post = await prisma.forumPost.findUnique({
          where: { id: postId },
        });

        if (!post) {
          const error = new Error('Post not found') as any;
          error.statusCode = 404;
          throw error;
        }

        const newReply = await prisma.forumReply.create({
          data: {
            content: content.trim(),
            authorId: session.user.id,
            postId: postId,
          },
          include: {
            author: {
              select: {
                id: true,
                email: true,
                name: true,
                profile: {
                  select: {
                    profilePictureUrl: true,
                    forumReputation: true,
                    forumPostCount: true,
                    forumReplyCount: true,
                    currentCareerPath: true,
                    progressLevel: true,
                  },
                },
              },
            },
          },
        });

        return NextResponse.json({
          success: true,
          data: newReply
        });
      }
    );
  });
});
