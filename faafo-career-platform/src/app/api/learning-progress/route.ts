import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { prisma } from '@/lib/prisma';
import { withUnifiedErrorHandling } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const session = await getServerSession(authOptions);

  if (!session || !session.user?.id) {
    const error = new Error('Unauthorized') as any;
    error.statusCode = 401;
    throw error;
  }

    const { searchParams } = new URL(request.url);
    const resourceId = searchParams.get('resourceId');

    if (resourceId) {
      // Get progress for specific resource
      const progress = await prisma.userLearningProgress.findUnique({
        where: {
          userId_resourceId: {
            userId: session.user.id,
            resourceId: resourceId
          }
        },
        include: {
          resource: {
            select: {
              id: true,
              title: true,
              url: true
            }
          }
        }
      });

      return NextResponse.json({
        success: true,
        data: progress
      });
    } else {
      // Get all progress for user
      const allProgress = await prisma.userLearningProgress.findMany({
        where: {
          userId: session.user.id
        },
        include: {
          resource: {
            select: {
              id: true,
              title: true,
              url: true,
              category: true,
              skillLevel: true
            }
          }
        },
        orderBy: {
          updatedAt: 'desc'
        }
      });

    return NextResponse.json({
      success: true,
      data: allProgress
    });
  }
});

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
        const session = await getServerSession(authOptions);

    if (!session || !session.user?.id) {
      const error = new Error('Unauthorized') as any;
      error.statusCode = 401;
      throw error;
    }

    const body = await request.json();
    const { resourceId, status, notes, rating, review } = body;

    if (!resourceId || !status) {
      const error = new Error('Resource ID and status are required') as any;
      error.statusCode = 400;
      throw error;
    }

    // Validate status
    const validStatuses = ['NOT_STARTED', 'IN_PROGRESS', 'COMPLETED', 'BOOKMARKED'];
    if (!validStatuses.includes(status)) {
      const error = new Error('Invalid status') as any;
      error.statusCode = 400;
      throw error;
    }

    // Validate rating if provided
    if (rating && (rating < 1 || rating > 5)) {
      const error = new Error('Rating must be between 1 and 5') as any;
      error.statusCode = 400;
      throw error;
    }

    const progress = await prisma.userLearningProgress.upsert({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId: resourceId
        }
      },
      update: {
        status: status,
        notes: notes || undefined,
        rating: rating || undefined,
        review: review || undefined,
        completedAt: status === 'COMPLETED' ? new Date() : undefined,
        updatedAt: new Date()
      },
      create: {
        userId: session.user.id,
        resourceId: resourceId,
        status: status,
        notes: notes || undefined,
        rating: rating || undefined,
        review: review || undefined,
        completedAt: status === 'COMPLETED' ? new Date() : undefined
      },
      include: {
        resource: {
          select: {
            id: true,
            title: true,
            url: true
          }
        }
      }
    });

        return NextResponse.json({
          success: true,
          data: progress
        });
      }
    );
  });
});

export const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 },
      async () => {
        const session = await getServerSession(authOptions);
    
    if (!session || !session.user?.id) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    const { searchParams } = new URL(request.url);
    const resourceId = searchParams.get('resourceId');

    if (!resourceId) {
      return NextResponse.json(
        { success: false, error: 'Resource ID is required' },
        { status: 400 }
      );
    }

    await prisma.userLearningProgress.delete({
      where: {
        userId_resourceId: {
          userId: session.user.id,
          resourceId: resourceId
        }
      }
    });

        return NextResponse.json({
          success: true,
          message: 'Progress deleted successfully'
        });
      }
    );
  });
});
