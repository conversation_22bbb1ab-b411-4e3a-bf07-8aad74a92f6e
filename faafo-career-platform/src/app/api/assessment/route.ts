/* eslint-disable security/detect-object-injection */
import { NextResponse, NextRequest } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth'; // Import from the new shared file
import prisma from '@/lib/prisma'; // Assuming prisma client is at lib/prisma.ts
import { AssessmentStatus, Prisma } from '@prisma/client';
import { getQuestionByKey, Question as AssessmentQuestionConfig, getAllQuestions } from '@/lib/assessmentDefinition'; // Import shared definition
import { SecurityValidator } from '@/lib/validation';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';
interface AssessmentSaveRequest {
  currentStep: number;
  formData: { [key: string]: Prisma.JsonValue | null };
  status?: 'IN_PROGRESS' | 'COMPLETED'; // Optional: client might send status for final submission
}

interface AssessmentSubmitRequest {
  assessmentId: string;
  formData: { [key: string]: Prisma.JsonValue | null };
}

interface AssessmentSubmitResponse {
  message: string;
  assessmentId: string;
  status: AssessmentStatus;
}

const validateFormData = (formData: { [key: string]: Prisma.JsonValue | null }): { sanitizedData: { [key: string]: Prisma.JsonValue | null }, error: string | null } => {
  const validKeys = getAllQuestions().map(q => q.key);
  const sanitizedData: { [key: string]: Prisma.JsonValue | null } = {};

  for (const key of Object.keys(formData)) {
    // Security: Validate the key itself for malicious patterns
    const isTestEnvironment = process.env.NODE_ENV === 'test';
    const keyValidation = SecurityValidator.validateSecurity(key, { isTestEnvironment });
    if (!keyValidation.isValid) {
      return {
        sanitizedData: {},
        error: `Security threat detected in question key '${key}': ${keyValidation.threats.join(', ')}.`
      };
    }

    if (!validKeys.includes(key)) {
      return { sanitizedData: {}, error: `Invalid question key submitted: ${key}.` };
    }
    const questionConfig = getQuestionByKey(key) as AssessmentQuestionConfig | undefined;
    if (!questionConfig) {
      return { sanitizedData: {}, error: `Invalid question key submitted: ${key}.` };
    }
    const value = formData[key];

    // Check required fields
    if (questionConfig.required) {
      if (value === null || value === undefined || (Array.isArray(value) && value.length === 0) || String(value).trim() === '') {
        return { sanitizedData: {}, error: `Missing required answer for question: ${questionConfig.text} (key: ${key}).` };
      }
    }

    // Validate data types and values based on question type
    if (value !== null && value !== undefined) {
      if (questionConfig.type === 'multipleChoice') {
        const mcQuestion = questionConfig as any;
        if (mcQuestion.allowMultiple) {
          // Should be an array
          if (!Array.isArray(value)) {
            return { sanitizedData: {}, error: `Question '${key}' expects an array of values, but received: ${typeof value}.` };
          }
          // Check if all values are valid options and sanitize them
          if (mcQuestion.options) {
            const validOptions = mcQuestion.options.map((opt: any) => opt.value);
            const sanitizedArray: string[] = [];
            for (const val of value) {
              // Security: Validate each array element
              if (typeof val === 'string') {
                const valValidation = SecurityValidator.validateSecurity(val, { isTestEnvironment });
                if (!valValidation.isValid) {
                  return {
                    sanitizedData: {},
                    error: `Security threat detected in answer for '${key}': ${valValidation.threats.join(', ')}.`
                  };
                }
                const sanitizedVal = SecurityValidator.sanitizeInput(val, { maxLength: 1000 });
                if (!validOptions.includes(sanitizedVal)) {
                  return { sanitizedData: {}, error: `Invalid option '${sanitizedVal}' for question '${key}'. Valid options: ${validOptions.join(', ')}.` };
                }
                sanitizedArray.push(sanitizedVal);
              } else if (typeof val === 'string' && validOptions.includes(val)) {
                sanitizedArray.push(val);
              } else {
                return { sanitizedData: {}, error: `Invalid option '${val}' for question '${key}'. Valid options: ${validOptions.join(', ')}.` };
              }
            }
            sanitizedData[key] = sanitizedArray;
          } else {
            sanitizedData[key] = value;
          }
        } else {
          // Should be a single string value
          if (typeof value !== 'string') {
            return { sanitizedData: {}, error: `Question '${key}' expects a string value, but received: ${typeof value}.` };
          }

          // Security: Validate and sanitize the string value
          const valueValidation = SecurityValidator.validateSecurity(value, { isTestEnvironment });
          if (!valueValidation.isValid) {
            return {
              sanitizedData: {},
              error: `Security threat detected in answer for '${key}': ${valueValidation.threats.join(', ')}.`
            };
          }

          const sanitizedValue = SecurityValidator.sanitizeInput(value, { maxLength: 1000 });

          // Check if value is a valid option
          if (mcQuestion.options) {
            const validOptions = mcQuestion.options.map((opt: any) => opt.value);
            if (!validOptions.includes(sanitizedValue)) {
              return { sanitizedData: {}, error: `Invalid option '${sanitizedValue}' for question '${key}'. Valid options: ${validOptions.join(', ')}.` };
            }
          }
          sanitizedData[key] = sanitizedValue;
        }
      } else if (questionConfig.type === 'scale') {
        // Should be a number
        if (typeof value !== 'number') {
          return { sanitizedData: {}, error: `Question '${key}' expects a number value, but received: ${typeof value}.` };
        }
        const scQuestion = questionConfig as any;
        if (scQuestion.numberOfSteps && (value < 1 || value > scQuestion.numberOfSteps)) {
          return { sanitizedData: {}, error: `Question '${key}' expects a value between 1 and ${scQuestion.numberOfSteps}, but received: ${value}.` };
        }
        sanitizedData[key] = value;
      } else if (questionConfig.type === 'text') {
        // Should be a string
        if (typeof value !== 'string') {
          return { sanitizedData: {}, error: `Question '${key}' expects a string value, but received: ${typeof value}.` };
        }

        // Security: Validate and sanitize text input
        const valueValidation = SecurityValidator.validateSecurity(value, { isTestEnvironment });
        if (!valueValidation.isValid) {
          return {
            sanitizedData: {},
            error: `Security threat detected in answer for '${key}': ${valueValidation.threats.join(', ')}.`
          };
        }

        const textQuestion = questionConfig as any;
        const maxLength = textQuestion.maxLength || 10000;
        const sanitizedValue = SecurityValidator.sanitizeInput(value, {
          maxLength,
          preserveNewlines: true
        });

        if (textQuestion.maxLength && sanitizedValue.length > textQuestion.maxLength) {
          return { sanitizedData: {}, error: `Question '${key}' text is too long. Maximum length: ${textQuestion.maxLength}.` };
        }
        if (textQuestion.minLength && sanitizedValue.length < textQuestion.minLength) {
          return { sanitizedData: {}, error: `Question '${key}' text is too short. Minimum length: ${textQuestion.minLength}.` };
        }
        sanitizedData[key] = sanitizedValue;
      } else {
        sanitizedData[key] = value;
      }
    } else {
      sanitizedData[key] = value;
    }
  }
  return { sanitizedData, error: null }; // Return sanitized data and no error
};

interface AssessmentStatusResponse {
  status: string;
  id?: string;
  completedAt?: Date | null;
  updatedAt?: Date;
  message?: string;
}

interface AssessmentDataResponse {
  currentStep: number;
  formData: { [key: string]: Prisma.JsonValue | null };
  status: AssessmentStatus;
  updatedAt: Date;
  id: string;
}

// GET handler to retrieve current user's active assessment
export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<AssessmentStatusResponse | AssessmentDataResponse>>> => {
  const session = await getServerSession(authOptions);

  if (!session || !session.user || !session.user.id) {
    const error = new Error('Unauthorized') as any;
    error.statusCode = 401;
    throw error;
  }

  // Check if this is a status check request
  const url = new URL(request.url);
  const checkStatus = url.searchParams.get('status') === 'true';

  if (checkStatus) {
    // Check for any assessment (completed or in-progress)
    const assessment = await prisma.assessment.findFirst({
      where: {
        userId: session.user.id,
      },
      select: {
        id: true,
        status: true,
        completedAt: true,
        updatedAt: true,
      },
      orderBy: {
        updatedAt: 'desc',
      },
    });

    if (!assessment) {
      return NextResponse.json({
        success: true,
        data: {
          status: 'NOT_FOUND',
          message: 'No assessment found for this user.'
        }
      });
    }

    return NextResponse.json({
      success: true,
      data: {
        status: assessment.status,
        id: assessment.id,
        completedAt: assessment.completedAt,
        updatedAt: assessment.updatedAt,
      }
    });
  }

  // Original logic for in-progress assessments
  const assessment = await prisma.assessment.findFirst({
    where: {
      userId: session.user.id,
      status: 'IN_PROGRESS', // Only look for in-progress assessments for the assessment taking flow
    },
    include: {
      responses: true, // Include all responses associated with this assessment
    },
    orderBy: {
      updatedAt: 'desc', // Get the most recently updated in-progress assessment if multiple somehow exist
    },
  });

  if (!assessment) {
    const error = new Error('No active assessment found for this user.') as any;
    error.statusCode = 404;
    throw error;
  }

  // Transform responses into a more convenient formData-like structure if needed by client
  // For now, returning the raw structure
  const formData: { [key: string]: Prisma.JsonValue | null } = {};
  assessment.responses.forEach(response => {
    formData[response.questionKey] = response.answerValue;
  });

  return NextResponse.json({
    success: true,
    data: {
      currentStep: assessment.currentStep,
      formData: formData,
      status: assessment.status,
      updatedAt: assessment.updatedAt,
      id: assessment.id,
    }
  });
});

interface AssessmentSaveResponse {
  message: string;
  assessmentId: string;
  status: AssessmentStatus;
}

// POST handler to save/update assessment progress
export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<AssessmentSaveResponse>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session || !session.user || !session.user.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }

        const body = await request.json() as AssessmentSaveRequest;
        const { currentStep, formData, status: newStatus } = body;

        if (typeof currentStep !== 'number' || !formData) {
          throw new Error('Invalid request body: currentStep and formData are required.');
        }

        const { sanitizedData, error } = validateFormData(formData);
        if (error) {
          throw new Error(error);
        }

        const userId = session.user.id;

        // Upsert logic for Assessment and AssessmentResponses
        const result = await prisma.$transaction(async (tx) => {
          let assessment = await tx.assessment.findFirst({
            where: {
              userId: userId,
              // If a COMPLETED assessment exists, user might want to start a new one.
              // For now, we assume only one IN_PROGRESS or find the latest if multiple exist.
              // This logic might need refinement based on product decisions (e.g., allow multiple assessments).
              status: 'IN_PROGRESS',
            },
            orderBy: {
              createdAt: 'desc' // Get the latest IN_PROGRESS one
            }
          });

          if (!assessment) {
            // If no IN_PROGRESS assessment, create a new one
            assessment = await tx.assessment.create({
              data: {
                userId: userId,
                currentStep: currentStep,
                status: newStatus === 'COMPLETED' ? 'COMPLETED' : 'IN_PROGRESS',
                // completedAt will be set by the PUT request for final submit
              },
            });
          } else {
            // Update existing assessment
            assessment = await tx.assessment.update({
              where: { id: assessment.id },
              data: {
                currentStep: currentStep,
                status: newStatus === 'COMPLETED' ? 'COMPLETED' : assessment.status, // Don't revert COMPLETED to IN_PROGRESS via POST
                updatedAt: new Date(),
              },
            });
          }

          // Process responses: delete existing for this assessment and recreate all based on formData
          // This is simpler than trying to diff and update individual responses.
          // For high-frequency saves, a more granular update might be better, but this is robust.
          await tx.assessmentResponse.deleteMany({
            where: { assessmentId: assessment.id },
          });

          const responseCreateData = Object.entries(sanitizedData).map(([key, value]) => ({
            assessmentId: assessment.id,
            questionKey: key,
            // Use Prisma.JsonNull for null values, otherwise pass the value directly.
            // The `value` can be string, number, array, or other JSON-compatible structures.
            answerValue: value === null || value === undefined ? Prisma.JsonNull : value,
          }));

          if (responseCreateData.length > 0) {
            await tx.assessmentResponse.createMany({
              data: responseCreateData,
            });
          }

          return assessment;
        });

        return NextResponse.json({
          success: true,
          data: {
            message: 'Progress saved successfully.',
            assessmentId: result.id,
            status: result.status
          }
        });
      }
    );
  }) as Promise<NextResponse<ApiResponse<AssessmentSaveResponse>>>;
});

export const PUT = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<AssessmentSubmitResponse>>> => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 30 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session || !session.user || !session.user.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }

        const body = await request.json() as AssessmentSubmitRequest;
        const { assessmentId, formData } = body;

        if (!assessmentId || !formData) {
          throw new Error('Invalid request body: assessmentId and formData are required.');
        }

        const { sanitizedData, error } = validateFormData(formData);
        if (error) {
          throw new Error(error);
        }

        const userId = session.user.id;

        const result = await prisma.$transaction(async (tx) => {
          const assessment = await tx.assessment.findUnique({
            where: { id: assessmentId, userId: userId },
          });

          if (!assessment) {
            const error = new Error('Assessment not found or user mismatch.') as any;
            error.statusCode = 404;
            throw error;
          }

          if (assessment.status === 'COMPLETED') {
            // Optionally allow re-submission or updates to completed assessments if needed
            // For now, let's assume completed assessments are final but can be re-saved with same data
            console.log(`Assessment ${assessmentId} is already completed. Re-saving final state.`);
          }

          const updatedAssessment = await tx.assessment.update({
            where: { id: assessmentId },
            data: {
              status: 'COMPLETED',
              completedAt: new Date(),
              updatedAt: new Date(), // Also update updatedAt
            },
          });

          // Delete existing responses and recreate all based on final formData
          await tx.assessmentResponse.deleteMany({
            where: { assessmentId: assessmentId },
          });

          const responseCreateData = Object.entries(sanitizedData).map(([key, value]) => ({
            assessmentId: assessmentId,
            questionKey: key,
            answerValue: value === null || value === undefined ? Prisma.JsonNull : value,
          }));

          if (responseCreateData.length > 0) {
            await tx.assessmentResponse.createMany({
              data: responseCreateData,
            });
          }
          return updatedAssessment;
        });

        return NextResponse.json({
          success: true,
          data: {
            message: 'Assessment submitted successfully.',
            assessmentId: result.id,
            status: result.status
          }
        });
      }
    );
  }) as Promise<NextResponse<ApiResponse<AssessmentSubmitResponse>>>;
});