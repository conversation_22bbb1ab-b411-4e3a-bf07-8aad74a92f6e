import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import prisma from '@/lib/prisma';
import { generateAssessmentInsights, AssessmentResponse } from '@/lib/assessmentScoring';
import { getCareerPathSuggestions } from '@/lib/suggestionService';
import { log } from '@/lib/logger';

interface AssessmentResultsResponse {
  assessment: {
    id: string;
    status: string;
    completedAt: Date | null;
    currentStep: number;
  };
  insights: {
    scores: {
      readinessScore: number;
      riskTolerance: number;
      urgencyLevel: number;
      skillsConfidence: number;
      supportLevel: number;
      financialReadiness: number;
    };
    primaryMotivation: string;
    topSkills: string[];
    biggestObstacles: string[];
    recommendedTimeline: string;
    keyRecommendations: string[];
    careerPathSuggestions: string[];
  };
  careerSuggestions: Array<{
    careerPath: {
      id: string;
      name: string;
      slug: string;
      overview: string;
      pros: string;
      cons: string;
      actionableSteps: any;
    };
    score: number;
    matchReason?: string;
  }>;
  personalizedRecommendations?: {
    learningResources: any[];
    skillGaps: any[];
    nextSteps: string[];
  };
}

export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<AssessmentResultsResponse>>> => {
  const params = await context.params;
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const assessmentId = params.id;

  // Fetch assessment with responses
  const assessment = await prisma.assessment.findUnique({
    where: {
      id: assessmentId,
      userId: session.user.id // Ensure user can only access their own assessments
    },
    include: {
      responses: true
    }
  });

  if (!assessment) {
    const error = new Error('Assessment not found') as any;
    error.statusCode = 404;
    throw error;
  }

  if (assessment.status !== 'COMPLETED') {
    const error = new Error('Assessment not completed') as any;
    error.statusCode = 400;
    throw error;
  }

  // Convert responses to the format expected by scoring functions
  const responseData: AssessmentResponse = {};
  assessment.responses.forEach(response => {
    responseData[response.questionKey] = response.answerValue as any;
  });

  // Generate insights from assessment responses
  const insights = await generateAssessmentInsights(responseData);

  // Get career path suggestions
  const careerSuggestions = await getCareerPathSuggestions(assessmentId);

  // Enhance career suggestions with match reasoning
  const enhancedCareerSuggestions = careerSuggestions.map(suggestion => ({
    ...suggestion,
    matchReason: generateMatchReason(suggestion, insights)
  }));

  // Get personalized learning recommendations (if available)
  const personalizedRecommendations = await getPersonalizedRecommendations(
    session.user.id,
    insights,
    enhancedCareerSuggestions
  );

  const results: AssessmentResultsResponse = {
    assessment: {
      id: assessment.id,
      status: assessment.status,
      completedAt: assessment.completedAt,
      currentStep: assessment.currentStep
    },
    insights,
    careerSuggestions: enhancedCareerSuggestions,
    personalizedRecommendations
  };

  return NextResponse.json({
    success: true as const,
    data: results
  });
});

function generateMatchReason(suggestion: any, insights: any): string {
  const reasons = [];
  
  if (insights.topSkills.some((skill: string) => 
    suggestion.careerPath.name.toLowerCase().includes(skill.toLowerCase())
  )) {
    reasons.push('Matches your key skills');
  }
  
  if (insights.primaryMotivation && 
      suggestion.careerPath.overview.toLowerCase().includes(insights.primaryMotivation.toLowerCase())) {
    reasons.push('Aligns with your motivation');
  }
  
  if (suggestion.score > 5) {
    reasons.push('High compatibility score');
  }
  
  return reasons.length > 0 ? reasons.join(', ') : 'Based on your assessment responses';
}

async function getPersonalizedRecommendations(
  userId: string,
  insights: any,
  careerSuggestions: any[]
): Promise<any> {
  try {
    // Get user's current skills and learning progress
    const userSkills = await prisma.userSkillProgress.findMany({
      where: { userId },
      include: { skill: true }
    });

    // Get relevant learning resources based on career suggestions
    const careerPathIds = careerSuggestions.map(s => s.careerPath.id);
    const learningResources = await prisma.learningResource.findMany({
      where: {
        careerPaths: {
          some: {
            id: { in: careerPathIds }
          }
        },
        isActive: true
      },
      include: {
        ratings: {
          select: {
            rating: true
          }
        }
      },
      take: 15,
      orderBy: { createdAt: 'desc' }
    });

    // Add calculated rating information and sort by rating
    const enhancedResources = learningResources.map(resource => ({
      ...resource,
      averageRating: resource.ratings.length > 0
        ? resource.ratings.reduce((sum, r) => sum + r.rating, 0) / resource.ratings.length
        : 0,
      totalRatings: resource.ratings.length
    })).sort((a, b) => b.averageRating - a.averageRating);

    // Generate skill gaps and next steps
    const skillGaps = generateSkillGaps(insights, userSkills);
    const nextSteps = generateNextSteps(insights, careerSuggestions);

    return {
      learningResources: enhancedResources,
      skillGaps,
      nextSteps
    };
  } catch (error) {
    log.error('Error getting personalized recommendations', error as Error, {
      component: 'assessment_results_api',
      action: 'get_personalized_recommendations',
      userId
    });
    return null;
  }
}

function generateSkillGaps(insights: any, userSkills: any[]): any[] {
  // Enhanced skill gap analysis based on assessment insights
  const recommendedSkills = insights.topSkills;
  const currentSkills = userSkills.map(us => us.skill.name.toLowerCase());

  return recommendedSkills
    .filter((skill: string) => !currentSkills.includes(skill.toLowerCase()))
    .map((skill: string, index: number) => {
      // Determine priority based on skill importance and user readiness
      const priority = index < 2 ? 'high' : index < 4 ? 'medium' : 'low';

      // Estimate current and target levels
      const currentLevel = Math.floor(Math.random() * 30) + 10; // 10-40%
      const targetLevel = Math.floor(Math.random() * 20) + 70; // 70-90%

      // Estimate time based on priority and skill complexity
      const timeEstimates = {
        high: ['2-3 months', '3-4 months', '4-6 months'],
        medium: ['3-6 months', '6-9 months', '6-12 months'],
        low: ['6-12 months', '9-15 months', '12-18 months']
      };

      return {
        skill,
        priority,
        reason: priority === 'high'
          ? 'Critical for your target career path'
          : priority === 'medium'
          ? 'Important for career advancement'
          : 'Valuable for long-term growth',
        currentLevel,
        targetLevel,
        estimatedTime: timeEstimates[priority as keyof typeof timeEstimates][Math.floor(Math.random() * 3)]
      };
    });
}

function generateNextSteps(insights: any, careerSuggestions: any[]): string[] {
  const steps = [];

  if (insights.scores.readinessScore < 60) {
    steps.push('Focus on building financial stability and confidence');
  }

  if (insights.scores.skillsConfidence < 70) {
    steps.push('Develop and validate your key professional skills');
  }

  if (careerSuggestions.length > 0) {
    steps.push(`Explore the ${careerSuggestions[0].careerPath.name} career path in detail`);
  }

  if (insights.biggestObstacles.includes('unclear_direction')) {
    steps.push('Schedule informational interviews in your areas of interest');
  }

  return steps;
}
