import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import prisma from '@/lib/prisma';
import { EnhancedAssessmentService } from '@/lib/enhancedAssessmentService';
import { AssessmentResponse } from '@/lib/assessmentScoring';
import { withCSRFProtection } from '@/lib/csrf';

interface EnhancedResultsResponse {
  insights: any;
  careerPathRecommendations: any[];
  learningPath: any;
  skillDevelopmentPlan: any;
  nextSteps: any[];
}

export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<EnhancedResultsResponse>>> => {
  const session = await getServerSession(authOptions);
  const { id: assessmentId } = await params;

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  if (!assessmentId) {
    const error = new Error('Assessment ID is required') as any;
    error.statusCode = 400;
    throw error;
  }

  // Verify assessment belongs to user
  const assessment = await prisma.assessment.findFirst({
    where: {
      id: assessmentId,
      userId: session.user.id
    },
    include: {
      responses: true
    }
  });

  if (!assessment) {
    const error = new Error('Assessment not found or access denied') as any;
    error.statusCode = 404;
    throw error;
  }

  if (assessment.status !== 'COMPLETED') {
    const error = new Error('Assessment is not completed') as any;
    error.statusCode = 400;
    throw error;
  }

  // Convert assessment responses to the expected format
  const responseData: AssessmentResponse = {};
  assessment.responses.forEach(response => {
    try {
      const value = typeof response.answerValue === 'string'
        ? JSON.parse(response.answerValue)
        : response.answerValue;
      responseData[response.questionKey] = value as string | string[] | number | null;
    } catch {
      responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
    }
  });

  // Generate enhanced results
  const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
    assessmentId,
    responseData
  );

  // Log successful generation
  console.log(`Enhanced assessment results generated for user ${session.user.id}, assessment ${assessmentId}`);

  return NextResponse.json({
    success: true as const,
    data: enhancedResults
  });
});

interface RegenerateResultsResponse {
  insights: any;
  careerPathRecommendations: any[];
  learningPath: any;
  skillDevelopmentPlan: any;
  nextSteps: any[];
  message: string;
}

// POST endpoint to regenerate results with updated preferences
export const POST = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<RegenerateResultsResponse>>> => {
  const session = await getServerSession(authOptions);
  const { id: assessmentId } = await params;
  const body = await request.json();

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  if (!assessmentId) {
    const error = new Error('Assessment ID is required') as any;
    error.statusCode = 400;
    throw error;
  }

  // Verify assessment belongs to user
  const assessment = await prisma.assessment.findFirst({
    where: {
      id: assessmentId,
      userId: session.user.id
    },
    include: {
      responses: true
    }
  });

  if (!assessment) {
    const error = new Error('Assessment not found or access denied') as any;
    error.statusCode = 404;
    throw error;
  }

  // Extract preferences from request body
  const {
    focusAreas = [],
    timeCommitment = 'MODERATE',
    budgetPreference = 'FREE_PREFERRED',
    learningStyle = 'MIXED'
  } = body;

  // Convert assessment responses to the expected format
  const responseData: AssessmentResponse = {};
  assessment.responses.forEach(response => {
    try {
      const value = typeof response.answerValue === 'string'
        ? JSON.parse(response.answerValue)
        : response.answerValue;
      responseData[response.questionKey] = value as string | string[] | number | null;
    } catch {
      responseData[response.questionKey] = response.answerValue as string | string[] | number | null;
    }
  });

  // Add user preferences to response data
  (responseData as any).user_preferences = {
    focusAreas,
    timeCommitment,
    budgetPreference,
    learningStyle
  };

  // Generate enhanced results with preferences
  const enhancedResults = await EnhancedAssessmentService.generateEnhancedResults(
    assessmentId,
    responseData
  );

  // Log successful regeneration
  console.log(`Enhanced assessment results regenerated with preferences for user ${session.user.id}, assessment ${assessmentId}`);

  return NextResponse.json({
    success: true as const,
    data: {
      ...enhancedResults,
      message: 'Results updated with your preferences'
    }
  });
});

interface FeedbackResponse {
  message: string;
  feedbackId?: string;
}

// PATCH endpoint to save user feedback on recommendations
export const PATCH = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<FeedbackResponse>>> => {
  const session = await getServerSession(authOptions);
  const { id: assessmentId } = await params;
  const body = await request.json();

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const {
    careerPathFeedback = {},
    resourceFeedback = {},
    overallRating,
    comments
  } = body;

  // Save feedback to database (you might want to create a feedback table)
  // For now, we'll log it and return success
  console.log(`Assessment feedback received from user ${session.user.id}:`, {
    assessmentId,
    careerPathFeedback,
    resourceFeedback,
    overallRating,
    comments
  });

  // In a production system, you would save this feedback to improve recommendations
  // await prisma.assessmentFeedback.create({
  //   data: {
  //     assessmentId,
  //     userId: session.user.id,
  //     careerPathFeedback,
  //     resourceFeedback,
  //     overallRating,
  //     comments
  //   }
  // });

  return NextResponse.json({
    success: true as const,
    data: {
      message: 'Thank you for your feedback! This helps us improve our recommendations.'
    }
  });
});
