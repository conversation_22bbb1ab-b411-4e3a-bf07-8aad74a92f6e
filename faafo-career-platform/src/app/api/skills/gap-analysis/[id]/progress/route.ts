import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { withCSRFProtection } from '@/lib/csrf';
import { prisma } from '@/lib/prisma';
import { z } from 'zod';

const updateProgressSchema = z.object({
  completedSkills: z.array(z.string()).min(1, 'At least one skill must be completed'),
  milestoneId: z.string().min(1, 'Milestone ID is required'),
  notes: z.string().max(1000, 'Notes too long').optional(),
});

interface UpdateGapAnalysisProgressRequest {
  completedSkills: string[];
  milestoneId: string;
  notes?: string;
}

interface UpdateGapAnalysisProgressResponse {
  success: boolean;
  data: {
    updatedAnalysis: {
      completionPercentage: number;
      nextMilestone: {
        skills: string[];
        estimatedHours: number;
        dueDate: string;
      };
    };
    achievements: Array<{
      type: string;
      title: string;
      points: number;
    }>;
  };
}

export const PUT = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<UpdateGapAnalysisProgressResponse['data']>>> => {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const userId = session.user.id;
  const { id: analysisId } = await params;

  const body = await request.json();
  const validation = updateProgressSchema.safeParse(body);

  if (!validation.success) {
    const error = new Error('Invalid progress update data') as any;
    error.statusCode = 400;
    error.details = validation.error.errors;
    throw error;
  }

  const { completedSkills, milestoneId, notes } = validation.data;
  // Get the gap analysis
  const analysis = await prisma.skillGapAnalysis.findFirst({
    where: {
      id: analysisId,
      userId,
    },
  });

  if (!analysis) {
    const error = new Error('Gap analysis not found') as any;
    error.statusCode = 404;
    throw error;
  }

  if (analysis.status !== 'ACTIVE') {
    const error = new Error('Cannot update inactive analysis') as any;
    error.statusCode = 400;
    throw error;
  }

  // Parse current progress tracking
  const progressTracking = analysis.progressTracking as any || {
    milestones: [],
    completedMilestones: [],
    completedSkills: [],
    currentPhase: 'planning',
  };

  // Update completed skills
  const newCompletedSkills = Array.from(new Set([
    ...(progressTracking.completedSkills || []),
    ...completedSkills,
  ]));

  // Mark milestone as completed
  const completedMilestones = Array.from(new Set([
    ...(progressTracking.completedMilestones || []),
    milestoneId,
  ]));

  // Calculate completion percentage
  const totalSkillGaps = Array.isArray(analysis.skillGaps) ? analysis.skillGaps.length : 0;
  const completionPercentage = totalSkillGaps > 0
    ? Math.round((newCompletedSkills.length / totalSkillGaps) * 100)
    : 0;

  // Update progress tracking
  const updatedProgressTracking = {
    ...progressTracking,
    completedSkills: newCompletedSkills,
    completedMilestones,
    lastUpdated: new Date().toISOString(),
    notes: notes ? [...(progressTracking.notes || []), {
      date: new Date().toISOString(),
      milestone: milestoneId,
      note: notes,
    }] : progressTracking.notes,
  };

  // Find next milestone
  const milestones = progressTracking.milestones || [];
  const nextMilestone = milestones.find((m: any) =>
    !completedMilestones.includes(m.month.toString())
  );

  // Update the analysis
  const updatedAnalysis = await prisma.skillGapAnalysis.update({
    where: { id: analysisId },
    data: {
      progressTracking: updatedProgressTracking,
      completionPercentage,
      lastUpdated: new Date(),
      status: completionPercentage >= 100 ? 'COMPLETED' : 'ACTIVE',
    },
  });

  // Update user skill progress for completed skills
  const achievements = [];
  for (const skillName of completedSkills) {
    try {
      // Find skill by name
      const skill = await prisma.skill.findFirst({
        where: { name: { contains: skillName, mode: 'insensitive' } },
      });

      if (skill) {
        // Update user skill progress
        await prisma.userSkillProgress.upsert({
          where: {
            userId_skillId: {
              userId,
              skillId: skill.id,
            },
          },
          update: {
            progressPoints: { increment: 25 }, // Bonus points for gap analysis completion
            lastPracticed: new Date(),
          },
          create: {
            userId,
            skillId: skill.id,
            currentLevel: 'INTERMEDIATE',
            progressPoints: 25,
            lastPracticed: new Date(),
          },
        });

        achievements.push({
          type: 'SKILL_PROGRESS',
          title: `${skillName} Progress`,
          points: 25,
        });
      }
    } catch (error) {
      console.error(`Error updating progress for skill ${skillName}:`, error);
    }
  }

  // Add milestone completion achievement
  achievements.push({
    type: 'MILESTONE_COMPLETED',
    title: `Milestone ${milestoneId} Completed`,
    points: 50,
  });

  // Prepare response data
  const responseData = {
    updatedAnalysis: {
      completionPercentage,
      nextMilestone: nextMilestone ? {
        skills: nextMilestone.skills || [],
        estimatedHours: nextMilestone.estimatedHours || 0,
        dueDate: calculateMilestoneDueDate(analysis.createdAt, nextMilestone.month),
      } : {
        skills: [],
        estimatedHours: 0,
        dueDate: new Date().toISOString(),
      },
    },
    achievements,
  };

  return NextResponse.json({
    success: true as const,
    data: responseData
  });
});

function calculateMilestoneDueDate(startDate: Date, milestoneMonth: number): string {
  const dueDate = new Date(startDate);
  dueDate.setMonth(dueDate.getMonth() + milestoneMonth);
  return dueDate.toISOString();
}
