import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withRateLimit } from '@/lib/rateLimit';
import { prisma } from '@/lib/prisma';

interface UserGapAnalysesResponse {
  success: boolean;
  data: {
    analyses: Array<{
      id: string;
      targetCareerPath: string;
      status: string;
      completionPercentage: number;
      createdAt: string;
      lastUpdated: string;
      expiresAt: string;
    }>;
    activeAnalysis?: {
      id: string;
      skillGaps: number;
      completedMilestones: number;
      totalMilestones: number;
      nextMilestone: {
        skills: string[];
        dueDate: string;
      };
    };
  };
}

async function handleGetUserGapAnalyses(request: NextRequest): Promise<NextResponse> {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  const userId = session.user.id;
  // Get all gap analyses for the user
  const analyses = await prisma.skillGapAnalysis.findMany({
    where: {
      userId,
    },
    orderBy: {
      createdAt: 'desc',
    },
  });

    const analysesData = analyses.map(analysis => ({
      id: analysis.id,
      targetCareerPath: analysis.targetCareerPathName,
      status: analysis.status,
      completionPercentage: analysis.completionPercentage,
      createdAt: analysis.createdAt.toISOString(),
      lastUpdated: analysis.lastUpdated.toISOString(),
      expiresAt: analysis.expiresAt.toISOString(),
    }));

    // Find active analysis
    const activeAnalysis = analyses.find(a => a.status === 'ACTIVE');
    let activeAnalysisData;

    if (activeAnalysis) {
      const progressTracking = activeAnalysis.progressTracking as any;
      const milestones = progressTracking?.milestones || [];
      const completedMilestones = progressTracking?.completedMilestones || [];
      const skillGaps = Array.isArray(activeAnalysis.skillGaps) ? activeAnalysis.skillGaps.length : 0;

      // Find next milestone
      const nextMilestone = milestones.find((m: any) => 
        !completedMilestones.includes(m.month)
      );

      activeAnalysisData = {
        id: activeAnalysis.id,
        skillGaps,
        completedMilestones: completedMilestones.length,
        totalMilestones: milestones.length,
        nextMilestone: nextMilestone ? {
          skills: nextMilestone.skills || [],
          dueDate: calculateMilestoneDueDate(activeAnalysis.createdAt, nextMilestone.month),
        } : {
          skills: [],
          dueDate: new Date().toISOString(),
        },
      };
    }

  const responseData: UserGapAnalysesResponse = {
    success: true,
    data: {
      analyses: analysesData,
      activeAnalysis: activeAnalysisData,
    },
  };

  return NextResponse.json(responseData);
}

function calculateMilestoneDueDate(startDate: Date, milestoneMonth: number): string {
  const dueDate = new Date(startDate);
  dueDate.setMonth(dueDate.getMonth() + milestoneMonth);
  return dueDate.toISOString();
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withRateLimit(
    request,
    { windowMs: 15 * 60 * 1000, maxRequests: 60 }, // 60 requests per 15 minutes
    () => handleGetUserGapAnalyses(request)
  );
});
