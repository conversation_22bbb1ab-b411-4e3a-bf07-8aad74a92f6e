import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { personalAnalyticsService } from '@/lib/personal-analytics-service';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface TestAnalyticsResponse {
  userId: string;
  tests: {
    learning?: any;
    career?: any;
    community?: any;
    goals?: any;
    comprehensive?: any;
  };
}

// Force dynamic rendering for this API route
export const dynamic = 'force-dynamic';

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

  console.log('Testing personal analytics for user:', session.user.id);

  // Test each method individually
  const results: TestAnalyticsResponse = {
    userId: session.user.id,
    tests: {}
  };

  // Test learning metrics
  console.log('Testing learning metrics...');
  results.tests.learning = await personalAnalyticsService.getLearningMetrics(session.user.id, 30);
  console.log('Learning metrics success');

  // Test career metrics
  console.log('Testing career metrics...');
  results.tests.career = await personalAnalyticsService.getCareerMetrics(session.user.id);
  console.log('Career metrics success');

  // Test community metrics
  console.log('Testing community metrics...');
  results.tests.community = await personalAnalyticsService.getCommunityMetrics(session.user.id, 30);
  console.log('Community metrics success');

  // Test goals metrics
  console.log('Testing goals metrics...');
  results.tests.goals = await personalAnalyticsService.getGoalsMetrics(session.user.id);
  console.log('Goals metrics success');

  return NextResponse.json({
    success: true as const,
    data: results
  });
});
