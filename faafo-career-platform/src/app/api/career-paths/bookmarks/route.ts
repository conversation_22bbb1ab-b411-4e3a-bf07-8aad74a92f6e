import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import prisma from '@/lib/prisma';
import { withCSRFProtection } from '@/lib/csrf';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface BookmarkedCareerPath {
  id: string;
  bookmarkedAt: Date;
  careerPath: any;
}

interface BookmarksResponse {
  bookmarks: BookmarkedCareerPath[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

interface DeleteBookmarkResponse {
  success: boolean;
  message: string;
}

// GET /api/career-paths/bookmarks - Get user's bookmarked career paths
export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.id) {
    const error = new Error('Authentication required') as any;
    error.statusCode = 401;
    throw error;
  }

    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const skip = (page - 1) * limit;

    // Get user's bookmarked career paths
    const bookmarks = await prisma.careerPathBookmark.findMany({
      where: {
        userId: session.user.id,
      },
      include: {
        careerPath: {
          include: {
            learningResources: {
              where: { isActive: true },
              select: {
                id: true,
                title: true,
                skillLevel: true,
                cost: true
              },
              take: 3
            },
            relatedSkills: {
              select: {
                id: true,
                name: true
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc',
      },
      skip,
      take: limit,
    });

    // Get total count for pagination
    const totalCount = await prisma.careerPathBookmark.count({
      where: {
        userId: session.user.id,
      },
    });

    // Format the response
    const formattedBookmarks = bookmarks.map(bookmark => ({
      id: bookmark.id,
      bookmarkedAt: bookmark.createdAt,
      careerPath: {
        ...bookmark.careerPath,
        pros: JSON.parse(bookmark.careerPath.pros),
        cons: JSON.parse(bookmark.careerPath.cons),
        actionableSteps: Array.isArray(bookmark.careerPath.actionableSteps)
          ? bookmark.careerPath.actionableSteps
          : JSON.parse(String(bookmark.careerPath.actionableSteps) || '[]'),
        isBookmarked: true
      }
    }));

  return NextResponse.json({
    success: true,
    data: {
      bookmarks: formattedBookmarks,
      pagination: {
        page,
        limit,
        total: totalCount,
        totalPages: Math.ceil(totalCount / limit),
        hasNext: page * limit < totalCount,
        hasPrev: page > 1
      }
    }
  });
});

// DELETE /api/career-paths/bookmarks - Remove bookmark
export const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    const session = await getServerSession(authOptions);

    if (!session?.user?.id) {
      const error = new Error('Authentication required') as any;
      error.statusCode = 401;
      throw error;
    }

    const body = await request.json();
    const { careerPathId } = body;

    if (!careerPathId) {
      const error = new Error('Career path ID is required') as any;
      error.statusCode = 400;
      throw error;
    }

    // Remove bookmark
    const deletedBookmark = await prisma.careerPathBookmark.deleteMany({
      where: {
        userId: session.user.id,
        careerPathId,
      },
    });

    if (deletedBookmark.count === 0) {
      const error = new Error('Bookmark not found') as any;
      error.statusCode = 404;
      throw error;
    }

    return NextResponse.json({
      success: true,
      data: {
        success: true,
        message: 'Bookmark removed successfully'
      }
    });
  });
});
