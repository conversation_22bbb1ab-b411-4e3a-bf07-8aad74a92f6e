import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth/next';
import { authOptions } from '@/lib/auth';
import { PrismaClient } from '@prisma/client';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';
import { withCSRFProtection } from '@/lib/csrf';
import { withRateLimit } from '@/lib/rateLimit';

const prisma = new PrismaClient();

interface ResourceRatingsResponse {
  ratings: any[];
  averageRating: number;
  totalRatings: number;
}

interface UserRatingsResponse {
  userRatings: any[];
}

interface CreateRatingResponse {
  resourceRating: any;
}

interface DeleteRatingResponse {
  message: string;
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest) => {
  const { searchParams } = new URL(request.url);
  const resourceId = searchParams.get('resourceId');
  const userId = searchParams.get('userId');

  if (resourceId) {
    // Get ratings for specific resource
    const ratings = await prisma.resourceRating.findMany({
      where: {
        resourceId: resourceId
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            image: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    // Calculate average rating
    const averageRating = ratings.length > 0
      ? ratings.reduce((sum, rating) => sum + rating.rating, 0) / ratings.length
      : 0;

    return NextResponse.json({
      success: true,
      data: {
        ratings,
        averageRating: Math.round(averageRating * 10) / 10,
        totalRatings: ratings.length
      }
    });
  } else if (userId) {
    // Get all ratings by specific user
    const session = await getServerSession(authOptions);

    if (!session || !session.user?.id || session.user.id !== userId) {
      const error = new Error('Unauthorized') as any;
      error.statusCode = 401;
      throw error;
    }

    const userRatings = await prisma.resourceRating.findMany({
      where: {
        userId: userId
      },
      include: {
        resource: {
          select: {
            id: true,
            title: true,
            url: true,
            category: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });

    return NextResponse.json({
      success: true,
      data: userRatings
    });
  } else {
    const error = new Error('Resource ID or User ID is required') as any;
    error.statusCode = 400;
    throw error;
  }
});

export const POST = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 20 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session || !session.user?.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }

        const body = await request.json();
        const { resourceId, rating, review, isHelpful } = body;

        if (!resourceId || !rating) {
          const error = new Error('Resource ID and rating are required') as any;
          error.statusCode = 400;
          throw error;
        }

        // Validate rating
        if (rating < 1 || rating > 5) {
          const error = new Error('Rating must be between 1 and 5') as any;
          error.statusCode = 400;
          throw error;
        }

        // Check if resource exists
        const resource = await prisma.learningResource.findUnique({
          where: { id: resourceId }
        });

        if (!resource) {
          const error = new Error('Resource not found') as any;
          error.statusCode = 404;
          throw error;
        }

        const resourceRating = await prisma.resourceRating.upsert({
          where: {
            userId_resourceId: {
              userId: session.user.id,
              resourceId: resourceId
            }
          },
          update: {
            rating: rating,
            review: review || undefined,
            isHelpful: isHelpful,
            updatedAt: new Date()
          },
          create: {
            userId: session.user.id,
            resourceId: resourceId,
            rating: rating,
            review: review || undefined,
            isHelpful: isHelpful
          },
          include: {
            user: {
              select: {
                id: true,
                name: true,
                image: true
              }
            },
            resource: {
              select: {
                id: true,
                title: true
              }
            }
          }
        });

        return NextResponse.json({
          success: true,
          data: resourceRating
        });
      }
    );
  });
});

export const DELETE = withUnifiedErrorHandling(async (request: NextRequest) => {
  return withCSRFProtection(request, async () => {
    return withRateLimit(
      request,
      { windowMs: 15 * 60 * 1000, maxRequests: 10 },
      async () => {
        const session = await getServerSession(authOptions);

        if (!session || !session.user?.id) {
          const error = new Error('Unauthorized') as any;
          error.statusCode = 401;
          throw error;
        }

        const { searchParams } = new URL(request.url);
        const resourceId = searchParams.get('resourceId');

        if (!resourceId) {
          const error = new Error('Resource ID is required') as any;
          error.statusCode = 400;
          throw error;
        }

        await prisma.resourceRating.delete({
          where: {
            userId_resourceId: {
              userId: session.user.id,
              resourceId: resourceId
            }
          }
        });

        return NextResponse.json({
          success: true,
          data: { message: 'Rating deleted successfully' }
        });
      }
    );
  });
});
