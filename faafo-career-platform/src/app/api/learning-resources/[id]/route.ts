import { NextRequest, NextResponse } from 'next/server';
import prisma from '@/lib/prisma';
import { apiCache } from '@/lib/cache';
import { withCSRFProtection } from '@/lib/csrf';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface LearningResourceResponse {
  id: string;
  title: string;
  description: string;
  url: string;
  type: string;
  category: string;
  skillLevel: string;
  author?: string | null;
  duration?: string | null;
  cost: string;
  averageRating: number;
  totalRatings: number;
  careerPaths: any[];
  skills: any[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface LearningResourceDeleteResponse {
  message: string;
}

export const GET = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<LearningResourceResponse>>> => {
  const { id } = await params;

  if (!id) {
    const error = new Error('Resource ID is required') as any;
    error.statusCode = 400;
    throw error;
  }

  // Check cache first
  const cacheKey = `learning_resource:${id}`;
  const cached = apiCache.getJSON(cacheKey) as LearningResourceResponse | null;
  if (cached) {
    return NextResponse.json({
      success: true,
      data: cached
    });
  }

    // Optimized query - get resource without ratings first
    const resource = await prisma.learningResource.findUnique({
      where: { id },
      select: {
        id: true,
        title: true,
        description: true,
        url: true,
        type: true,
        category: true,
        skillLevel: true,
        author: true,
        duration: true,
        cost: true,
        isActive: true,
        createdAt: true,
        updatedAt: true,
        careerPaths: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        skills: {
          select: {
            id: true,
            name: true,
          },
        },
      },
    });

  if (!resource) {
    const error = new Error('Resource not found') as any;
    error.statusCode = 404;
    throw error;
  }

    // Get rating aggregation in a separate optimized query
    const ratingAggregation = await prisma.resourceRating.aggregate({
      where: { resourceId: id },
      _avg: { rating: true },
      _count: { rating: true }
    });

    const averageRating = ratingAggregation._avg.rating || 0;
    const totalRatings = ratingAggregation._count.rating || 0;

    // Format the response
    const formattedResource = {
      id: resource.id,
      title: resource.title,
      description: resource.description,
      url: resource.url,
      type: resource.type,
      category: resource.category,
      skillLevel: resource.skillLevel,
      author: resource.author,
      duration: resource.duration,
      cost: resource.cost,
      averageRating: Math.round(averageRating * 10) / 10,
      totalRatings,
      careerPaths: resource.careerPaths,
      skills: resource.skills,
      isActive: resource.isActive,
      createdAt: resource.createdAt,
      updatedAt: resource.updatedAt,
    };

  // Cache the result for 10 minutes
  apiCache.setJSON(cacheKey, formattedResource, 10 * 60 * 1000);

  return NextResponse.json({
    success: true,
    data: formattedResource,
  });
});

export const PUT = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<LearningResourceResponse>>> => {
  const { id } = await params;
  const body = await request.json();

  if (!id) {
    const error = new Error('Resource ID is required') as any;
    error.statusCode = 400;
    throw error;
  }

    const {
      title,
      description,
      url,
      type,
      category,
      skillLevel,
      author,
      duration,
      cost,
      format,
      isActive,
    } = body;

  // Check if resource exists
  const existingResource = await prisma.learningResource.findUnique({
    where: { id },
  });

  if (!existingResource) {
    const error = new Error('Resource not found') as any;
    error.statusCode = 404;
    throw error;
  }

    const updatedResource = await prisma.learningResource.update({
      where: { id },
      data: {
        title: title || existingResource.title,
        description: description || existingResource.description,
        url: url || existingResource.url,
        type: type || existingResource.type,
        category: category || existingResource.category,
        skillLevel: skillLevel || existingResource.skillLevel,
        author: author !== undefined ? author : existingResource.author,
        duration: duration !== undefined ? duration : existingResource.duration,
        cost: cost || existingResource.cost,
        format: format || existingResource.format,
        isActive: isActive !== undefined ? isActive : existingResource.isActive,
        updatedAt: new Date(),
      },
      include: {
        careerPaths: {
          select: {
            id: true,
            name: true,
            slug: true,
          },
        },
        skills: {
          select: {
            id: true,
            name: true,
          },
        },
        ratings: {
          select: {
            rating: true,
          },
        },
      },
    });

    // Calculate average rating
    const averageRating = updatedResource.ratings.length > 0 
      ? updatedResource.ratings.reduce((sum, rating) => sum + rating.rating, 0) / updatedResource.ratings.length
      : 0;

    const totalRatings = updatedResource.ratings.length;

  const formattedResource = {
    ...updatedResource,
    averageRating: Math.round(averageRating * 10) / 10,
    totalRatings,
  };

  return NextResponse.json({
    success: true,
    data: formattedResource,
  });
});

export const DELETE = withUnifiedErrorHandling(async (
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
): Promise<NextResponse<ApiResponse<LearningResourceDeleteResponse>>> => {
  const { id } = await params;

  if (!id) {
    const error = new Error('Resource ID is required') as any;
    error.statusCode = 400;
    throw error;
  }

  // Check if resource exists
  const existingResource = await prisma.learningResource.findUnique({
    where: { id },
  });

  if (!existingResource) {
    const error = new Error('Resource not found') as any;
    error.statusCode = 404;
    throw error;
  }

  // Soft delete by setting isActive to false
  await prisma.learningResource.update({
    where: { id },
    data: {
      isActive: false,
      updatedAt: new Date(),
    },
  });

  return NextResponse.json({
    success: true,
    data: {
      message: 'Resource deleted successfully'
    }
  });
});
