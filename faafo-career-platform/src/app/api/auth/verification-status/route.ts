import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from "next-auth/next";
import { authOptions } from "@/lib/auth";
import prisma from '@/lib/prisma';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

// Force dynamic rendering for this route
export const dynamic = 'force-dynamic';

interface VerificationStatusResponse {
  isVerified: boolean;
  emailVerified: Date | null;
}

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<VerificationStatusResponse>>> => {
  const session = await getServerSession(authOptions);

  if (!session?.user?.email) {
    const error = new Error('Not authenticated.') as any;
    error.statusCode = 401;
    throw error;
  }

  const user = await prisma.user.findUnique({
    where: { email: session.user.email },
    select: {
      emailVerified: true,
    },
  });

  if (!user) {
    const error = new Error('User not found.') as any;
    error.statusCode = 404;
    throw error;
  }

  return NextResponse.json({
    success: true,
    data: {
      isVerified: !!user.emailVerified,
      emailVerified: user.emailVerified
    }
  });
});
