import { NextRequest, NextResponse } from 'next/server';
import { cookies } from 'next/headers';
import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

interface ForceLogoutResponse {
  message: string;
  clearedCookies: number;
}

/**
 * Force logout endpoint that aggressively clears all session data
 * This is useful for development when sessions get stuck
 */
export const POST = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<ForceLogoutResponse>>> => {
  console.log('Force logout requested');

  // Get all cookies
  const cookieStore = cookies();
  const allCookies = cookieStore.getAll();

  // Clear all NextAuth related cookies
  const nextAuthCookies = [
    'next-auth.session-token',
    'next-auth.csrf-token',
    'next-auth.callback-url',
    '__Secure-next-auth.session-token',
    '__Host-next-auth.csrf-token',
    'next-auth.pkce.code_verifier'
  ];

  // Create response data
  const responseData: ForceLogoutResponse = {
    message: 'Force logout completed',
    clearedCookies: allCookies.length
  };

  // Create response with proper ApiResponse structure
  const response = NextResponse.json({
    success: true as const,
    data: responseData
  });

  // Clear NextAuth cookies
  nextAuthCookies.forEach(cookieName => {
    response.cookies.set(cookieName, '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: false, // Set to false for localhost
      sameSite: 'lax'
    });

    response.cookies.set(cookieName, '', {
      expires: new Date(0),
      path: '/api/auth',
      httpOnly: true,
      secure: false,
      sameSite: 'lax'
    });
  });

  // Clear all existing cookies
  allCookies.forEach(cookie => {
    response.cookies.set(cookie.name, '', {
      expires: new Date(0),
      path: '/',
      httpOnly: true,
      secure: false,
      sameSite: 'lax'
    });
  });

  console.log(`Force logout completed, cleared ${allCookies.length} cookies`);

  return response;
});

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<{ message: string; endpoint: string; method: string }>>> => {
  return NextResponse.json({
    success: true as const,
    data: {
      message: 'Use POST method to force logout',
      endpoint: '/api/auth/force-logout',
      method: 'POST'
    }
  });
});
