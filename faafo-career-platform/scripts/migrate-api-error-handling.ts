#!/usr/bin/env tsx

/**
 * Migration Script: API Error Handling Standardization
 * 
 * This script helps identify and migrate API routes to use the unified error handling system.
 * It scans all API routes and provides a report of current error handling patterns.
 */

import fs from 'fs';
import path from 'path';

interface ApiRouteAnalysis {
  filePath: string;
  hasWithErrorHandler: boolean;
  hasWithSecureErrorHandling: boolean;
  hasWithErrorHandling: boolean;
  hasManualTryCatch: boolean;
  hasWithFriendlyErrorMessages: boolean;
  errorHandlingPatterns: string[];
  needsMigration: boolean;
}

class ApiErrorHandlingMigrator {
  private apiDir = 'src/app/api';
  private results: ApiRouteAnalysis[] = [];

  private analyzeFile(filePath: string): ApiRouteAnalysis {
    const content = fs.readFileSync(filePath, 'utf-8');
    
    const analysis: ApiRouteAnalysis = {
      filePath,
      hasWithErrorHandler: content.includes('withErrorHandler'),
      hasWithSecureErrorHandling: content.includes('withSecureErrorHandling'),
      hasWithErrorHandling: content.includes('withErrorHandling'),
      hasManualTryCatch: /try\s*{[\s\S]*?catch\s*\([^)]*\)\s*{/.test(content),
      hasWithFriendlyErrorMessages: content.includes('withFriendlyErrorMessages'),
      errorHandlingPatterns: [],
      needsMigration: false
    };

    // Identify specific patterns
    if (analysis.hasWithErrorHandler) {
      analysis.errorHandlingPatterns.push('withErrorHandler');
    }
    if (analysis.hasWithSecureErrorHandling) {
      analysis.errorHandlingPatterns.push('withSecureErrorHandling');
    }
    if (analysis.hasWithErrorHandling) {
      analysis.errorHandlingPatterns.push('withErrorHandling');
    }
    if (analysis.hasWithFriendlyErrorMessages) {
      analysis.errorHandlingPatterns.push('withFriendlyErrorMessages');
    }
    if (analysis.hasManualTryCatch) {
      analysis.errorHandlingPatterns.push('manual try-catch');
    }

    // Determine if migration is needed
    analysis.needsMigration = analysis.errorHandlingPatterns.length > 0;

    return analysis;
  }

  private scanDirectory(dir: string): void {
    const fullPath = path.join(process.cwd(), dir);
    
    if (!fs.existsSync(fullPath)) {
      console.error(`Directory not found: ${fullPath}`);
      return;
    }

    const items = fs.readdirSync(fullPath);

    for (const item of items) {
      const itemPath = path.join(fullPath, item);
      const relativePath = path.join(dir, item);
      const stat = fs.statSync(itemPath);

      if (stat.isDirectory()) {
        // Recursively scan subdirectories
        this.scanDirectory(relativePath);
      } else if (item === 'route.ts' || item === 'route.js') {
        // Analyze API route files
        const analysis = this.analyzeFile(itemPath);
        this.results.push(analysis);
      }
    }
  }

  private generateReport(): void {
    console.log('🔍 API Error Handling Migration Report\n');
    console.log('=====================================\n');

    const totalRoutes = this.results.length;
    const routesNeedingMigration = this.results.filter(r => r.needsMigration).length;
    const routesWithoutErrorHandling = this.results.filter(r => !r.needsMigration).length;

    console.log(`📊 Summary:`);
    console.log(`   Total API routes: ${totalRoutes}`);
    console.log(`   Routes needing migration: ${routesNeedingMigration}`);
    console.log(`   Routes without error handling: ${routesWithoutErrorHandling}`);
    console.log('');

    // Pattern analysis
    const patternCounts = new Map<string, number>();
    this.results.forEach(result => {
      result.errorHandlingPatterns.forEach(pattern => {
        patternCounts.set(pattern, (patternCounts.get(pattern) || 0) + 1);
      });
    });

    console.log(`📈 Error Handling Patterns:`);
    for (const [pattern, count] of patternCounts.entries()) {
      console.log(`   ${pattern}: ${count} routes`);
    }
    console.log('');

    // Routes needing migration
    if (routesNeedingMigration > 0) {
      console.log(`🔧 Routes Requiring Migration:`);
      this.results
        .filter(r => r.needsMigration)
        .forEach(result => {
          console.log(`   📁 ${result.filePath.replace(process.cwd() + '/', '')}`);
          console.log(`      Patterns: ${result.errorHandlingPatterns.join(', ')}`);
        });
      console.log('');
    }

    // Routes without error handling
    if (routesWithoutErrorHandling > 0) {
      console.log(`⚠️  Routes Without Error Handling:`);
      this.results
        .filter(r => !r.needsMigration)
        .forEach(result => {
          console.log(`   📁 ${result.filePath.replace(process.cwd() + '/', '')}`);
        });
      console.log('');
    }

    // Migration recommendations
    console.log(`💡 Migration Recommendations:`);
    console.log('');
    console.log('1. Replace all error handling patterns with withUnifiedErrorHandling:');
    console.log('   - Remove: withErrorHandler, withSecureErrorHandling, withErrorHandling');
    console.log('   - Replace with: withUnifiedErrorHandling');
    console.log('');
    console.log('2. Update imports:');
    console.log('   - Add: import { withUnifiedErrorHandling } from "@/lib/unified-api-error-handler";');
    console.log('   - Remove old error handler imports');
    console.log('');
    console.log('3. Standardize error responses:');
    console.log('   - Use ApiResponse<T> type for consistent response structure');
    console.log('   - Replace manual error responses with standard format');
    console.log('');
    console.log('4. Remove manual try-catch blocks:');
    console.log('   - Let withUnifiedErrorHandling handle all errors');
    console.log('   - Focus on business logic in route handlers');
    console.log('');

    // Generate migration checklist
    this.generateMigrationChecklist();
  }

  private generateMigrationChecklist(): void {
    const checklistPath = path.join(process.cwd(), 'API_ERROR_HANDLING_MIGRATION_CHECKLIST.md');
    
    let checklist = `# API Error Handling Migration Checklist\n\n`;
    checklist += `Generated on: ${new Date().toISOString()}\n\n`;
    checklist += `## Overview\n\n`;
    checklist += `- Total routes: ${this.results.length}\n`;
    checklist += `- Routes needing migration: ${this.results.filter(r => r.needsMigration).length}\n\n`;
    checklist += `## Migration Tasks\n\n`;

    this.results
      .filter(r => r.needsMigration)
      .forEach((result, index) => {
        const relativePath = result.filePath.replace(process.cwd() + '/', '');
        checklist += `### ${index + 1}. ${relativePath}\n\n`;
        checklist += `- [ ] Replace error handling patterns: ${result.errorHandlingPatterns.join(', ')}\n`;
        checklist += `- [ ] Update imports to use withUnifiedErrorHandling\n`;
        checklist += `- [ ] Remove manual try-catch blocks\n`;
        checklist += `- [ ] Standardize error response format\n`;
        checklist += `- [ ] Test error scenarios\n\n`;
      });

    checklist += `## Post-Migration Verification\n\n`;
    checklist += `- [ ] All API routes use withUnifiedErrorHandling\n`;
    checklist += `- [ ] No manual try-catch blocks in route handlers\n`;
    checklist += `- [ ] Consistent error response format across all routes\n`;
    checklist += `- [ ] Error logging and tracking working correctly\n`;
    checklist += `- [ ] Development vs production error details properly handled\n`;
    checklist += `- [ ] All error scenarios tested\n\n`;

    fs.writeFileSync(checklistPath, checklist);
    console.log(`📋 Migration checklist saved to: ${checklistPath}`);
  }

  public run(): void {
    console.log('🚀 Starting API Error Handling Migration Analysis...\n');
    
    this.scanDirectory(this.apiDir);
    this.generateReport();
    
    console.log('✅ Analysis complete!');
  }
}

// Example migration for a single route
function generateExampleMigration(): void {
  console.log('\n📝 Example Migration:\n');
  console.log('BEFORE:');
  console.log('```typescript');
  console.log(`import { withErrorHandler } from '@/lib/errorHandler';

export const GET = withErrorHandler(async (request: NextRequest) => {
  try {
    const session = await getServerSession(authOptions);
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    const data = await prisma.someModel.findMany();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Error:', error);
    return NextResponse.json({ error: 'Internal error' }, { status: 500 });
  }
});`);
  console.log('```\n');
  
  console.log('AFTER:');
  console.log('```typescript');
  console.log(`import { withUnifiedErrorHandling, ApiResponse } from '@/lib/unified-api-error-handler';

export const GET = withUnifiedErrorHandling(async (request: NextRequest): Promise<NextResponse<ApiResponse<any>>> => {
  const session = await getServerSession(authOptions);
  if (!session?.user?.id) {
    throw new Error('Unauthorized');
  }
  
  const data = await prisma.someModel.findMany();
  return NextResponse.json({ success: true, data });
});`);
  console.log('```\n');
}

// Run the migration analysis
if (require.main === module) {
  const migrator = new ApiErrorHandlingMigrator();
  migrator.run();
  generateExampleMigration();
}
