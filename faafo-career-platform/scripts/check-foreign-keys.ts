#!/usr/bin/env tsx

import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

async function checkForeignKeyConstraints() {
  console.log('🔍 Checking for orphaned records and foreign key constraint violations...\n');

  try {
    // Check Resume.userId foreign key
    console.log('1. Checking Resume.userId foreign key...');
    const resumesWithoutUser = await prisma.$queryRaw`
      SELECT r.id, r."userId"
      FROM "Resume" r
      LEFT JOIN "User" u ON r."userId" = u.id
      WHERE u.id IS NULL
    `;

    if (Array.isArray(resumesWithoutUser) && resumesWithoutUser.length > 0) {
      console.log(`❌ Found ${resumesWithoutUser.length} orphaned Resume records:`);
      resumesWithoutUser.forEach((record: any) => {
        console.log(`   Resume ID: ${record.id}, User ID: ${record.userId}`);
      });
    } else {
      console.log('✅ No orphaned Resume records found');
    }

    // Check Assessment.userId foreign key
    console.log('\n2. Checking Assessment.userId foreign key...');
    const assessmentsWithoutUser = await prisma.$queryRaw`
      SELECT a.id, a."userId"
      FROM "Assessment" a
      LEFT JOIN "User" u ON a."userId" = u.id
      WHERE u.id IS NULL
    `;

    if (Array.isArray(assessmentsWithoutUser) && assessmentsWithoutUser.length > 0) {
      console.log(`❌ Found ${assessmentsWithoutUser.length} orphaned Assessment records:`);
      assessmentsWithoutUser.forEach((record: any) => {
        console.log(`   Assessment ID: ${record.id}, User ID: ${record.userId}`);
      });
    } else {
      console.log('✅ No orphaned Assessment records found');
    }

    // Check SkillAssessment.userId foreign key
    console.log('\n3. Checking SkillAssessment.userId foreign key...');
    const skillAssessmentsWithoutUser = await prisma.$queryRaw`
      SELECT sa.id, sa."userId"
      FROM "SkillAssessment" sa
      LEFT JOIN "User" u ON sa."userId" = u.id
      WHERE u.id IS NULL
    `;

    if (Array.isArray(skillAssessmentsWithoutUser) && skillAssessmentsWithoutUser.length > 0) {
      console.log(`❌ Found ${skillAssessmentsWithoutUser.length} orphaned SkillAssessment records:`);
      skillAssessmentsWithoutUser.forEach((record: any) => {
        console.log(`   SkillAssessment ID: ${record.id}, User ID: ${record.userId}`);
      });
    } else {
      console.log('✅ No orphaned SkillAssessment records found');
    }

    // Check InterviewSession.userId foreign key
    console.log('\n4. Checking InterviewSession.userId foreign key...');
    const interviewSessionsWithoutUser = await prisma.$queryRaw`
      SELECT i.id, i."userId"
      FROM "InterviewSession" i
      LEFT JOIN "User" u ON i."userId" = u.id
      WHERE u.id IS NULL
    `;

    if (Array.isArray(interviewSessionsWithoutUser) && interviewSessionsWithoutUser.length > 0) {
      console.log(`❌ Found ${interviewSessionsWithoutUser.length} orphaned InterviewSession records:`);
      interviewSessionsWithoutUser.forEach((record: any) => {
        console.log(`   InterviewSession ID: ${record.id}, User ID: ${record.userId}`);
      });
    } else {
      console.log('✅ No orphaned InterviewSession records found');
    }

    // Check UserGoal.userId foreign key
    console.log('\n5. Checking UserGoal.userId foreign key...');
    const userGoalsWithoutUser = await prisma.$queryRaw`
      SELECT ug.id, ug."userId"
      FROM "UserGoal" ug
      LEFT JOIN "User" u ON ug."userId" = u.id
      WHERE u.id IS NULL
    `;

    if (Array.isArray(userGoalsWithoutUser) && userGoalsWithoutUser.length > 0) {
      console.log(`❌ Found ${userGoalsWithoutUser.length} orphaned UserGoal records:`);
      userGoalsWithoutUser.forEach((record: any) => {
        console.log(`   UserGoal ID: ${record.id}, User ID: ${record.userId}`);
      });
    } else {
      console.log('✅ No orphaned UserGoal records found');
    }

    // Check database constraints directly
    console.log('\n6. Checking database foreign key constraints...');
    const constraints = await prisma.$queryRaw`
      SELECT 
        tc.table_name, 
        kcu.column_name, 
        ccu.table_name AS foreign_table_name,
        ccu.column_name AS foreign_column_name,
        tc.constraint_name
      FROM 
        information_schema.table_constraints AS tc 
        JOIN information_schema.key_column_usage AS kcu
          ON tc.constraint_name = kcu.constraint_name
        JOIN information_schema.constraint_column_usage AS ccu
          ON ccu.constraint_name = tc.constraint_name
      WHERE 
        tc.constraint_type = 'FOREIGN KEY' 
        AND tc.table_name IN ('Resume', 'Assessment', 'SkillAssessment', 'InterviewSession', 'UserGoal')
        AND kcu.column_name = 'userId'
      ORDER BY tc.table_name;
    `;

    if (Array.isArray(constraints) && constraints.length > 0) {
      console.log('✅ Found foreign key constraints in database:');
      constraints.forEach((constraint: any) => {
        console.log(`   ${constraint.table_name}.${constraint.column_name} -> ${constraint.foreign_table_name}.${constraint.foreign_column_name} (${constraint.constraint_name})`);
      });
    } else {
      console.log('❌ No foreign key constraints found in database');
    }

    console.log('\n✅ Foreign key constraint check completed!');

  } catch (error) {
    console.error('❌ Error checking foreign key constraints:', error);
  } finally {
    await prisma.$disconnect();
  }
}

checkForeignKeyConstraints();
