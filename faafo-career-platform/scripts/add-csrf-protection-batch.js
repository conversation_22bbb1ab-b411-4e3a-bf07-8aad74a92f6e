#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Routes that need CSRF protection added
const routesToProtect = [
  'src/app/api/assessment/[id]/ai-insights/route.ts',
  'src/app/api/assessment/[id]/enhanced-results/route.ts',
  'src/app/api/ai/health/route.ts',
  'src/app/api/ai/resume-analysis/route.ts',
  'src/app/api/ai/interview-prep/route.ts',
  'src/app/api/ai/skills-analysis/route.ts',
  'src/app/api/learning-resources/[id]/route.ts',
  'src/app/api/learning-resources/categories/route.ts',
  'src/app/api/career-paths/bookmarks/route.ts',
  'src/app/api/career-paths/route.ts',
  'src/app/api/interview-practice/[sessionId]/responses/route.ts',
  'src/app/api/interview-practice/[sessionId]/responses/[responseId]/route.ts',
  'src/app/api/interview-practice/[sessionId]/questions/route.ts',
  'src/app/api/learning-paths/route.ts',
  'src/app/api/learning-paths/[id]/enroll/route.ts',
  'src/app/api/learning-paths/[id]/steps/[stepId]/progress/route.ts',
  'src/app/api/learning-paths/[id]/route.ts',
  'src/app/api/analytics/dashboard/route.ts'
];

function addCSRFImport(content) {
  // Check if CSRF import already exists
  if (content.includes('withCSRFProtection')) {
    return content;
  }

  // Find the last import statement
  const lines = content.split('\n');
  let lastImportIndex = -1;
  
  for (let i = 0; i < lines.length; i++) {
    if (lines[i].trim().startsWith('import ') && !lines[i].includes('type ')) {
      lastImportIndex = i;
    }
  }

  if (lastImportIndex !== -1) {
    lines.splice(lastImportIndex + 1, 0, "import { withCSRFProtection } from '@/lib/csrf';");
  }

  return lines.join('\n');
}

function wrapHandlerWithCSRF(content, handlerType) {
  const handlerRegex = new RegExp(`export\\s+(async\\s+function\\s+${handlerType}|const\\s+${handlerType}\\s*=)`, 'g');
  
  return content.replace(handlerRegex, (match, captured) => {
    if (captured.includes('async function')) {
      // Handle: export async function POST(request: NextRequest) {
      return match.replace(/\{/, '{\n  return withCSRFProtection(request, async () => {');
    } else {
      // Handle: export const POST = withErrorHandler(async (request: NextRequest) => {
      const lines = content.split('\n');
      const handlerStartIndex = lines.findIndex(line => line.includes(match));
      
      if (handlerStartIndex !== -1) {
        // Find the opening brace and wrap the handler
        for (let i = handlerStartIndex; i < lines.length; i++) {
          if (lines[i].includes('async (request') || lines[i].includes('async (req')) {
            lines[i] = lines[i].replace('async (', 'async (request: NextRequest) => {\n    return withCSRFProtection(request, async (');
            break;
          }
        }
      }
      
      return match;
    }
  });
}

function addClosingBrace(content, handlerType) {
  const lines = content.split('\n');
  let braceCount = 0;
  let handlerStarted = false;
  let handlerEndIndex = -1;

  for (let i = 0; i < lines.length; i++) {
    const line = lines[i];
    
    if (line.includes(`export`) && (line.includes(`${handlerType}`) || line.includes(`function ${handlerType}`))) {
      handlerStarted = true;
      continue;
    }

    if (handlerStarted) {
      // Count braces
      const openBraces = (line.match(/\{/g) || []).length;
      const closeBraces = (line.match(/\}/g) || []).length;
      braceCount += openBraces - closeBraces;

      if (braceCount === 0 && line.includes('}')) {
        handlerEndIndex = i;
        break;
      }
    }
  }

  if (handlerEndIndex !== -1) {
    lines[handlerEndIndex] = lines[handlerEndIndex].replace(/^(\s*)\}/, '$1}\n$1});');
  }

  return lines.join('\n');
}

function processFile(filePath) {
  console.log(`Processing: ${filePath}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`  ❌ File not found: ${filePath}`);
    return false;
  }

  let content = fs.readFileSync(filePath, 'utf8');
  
  // Check if already has CSRF protection
  if (content.includes('withCSRFProtection')) {
    console.log(`  ✅ Already has CSRF protection`);
    return true;
  }

  // Add CSRF import
  content = addCSRFImport(content);

  // Find and wrap POST, PUT, PATCH, DELETE handlers
  const handlers = ['POST', 'PUT', 'PATCH', 'DELETE'];
  let modified = false;

  for (const handler of handlers) {
    const handlerRegex = new RegExp(`export\\s+(async\\s+function\\s+${handler}|const\\s+${handler}\\s*=)`, 'g');
    if (handlerRegex.test(content)) {
      console.log(`  🔧 Adding CSRF protection to ${handler} handler`);
      content = wrapHandlerWithCSRF(content, handler);
      content = addClosingBrace(content, handler);
      modified = true;
    }
  }

  if (modified) {
    fs.writeFileSync(filePath, content);
    console.log(`  ✅ CSRF protection added`);
    return true;
  } else {
    console.log(`  ℹ️  No state-changing handlers found`);
    return true;
  }
}

// Main execution
console.log('🔒 Adding CSRF Protection to API Routes\n');

let successCount = 0;
let totalCount = routesToProtect.length;

for (const route of routesToProtect) {
  if (processFile(route)) {
    successCount++;
  }
}

console.log(`\n📊 Summary:`);
console.log(`Total routes processed: ${totalCount}`);
console.log(`Successfully updated: ${successCount}`);
console.log(`Failed: ${totalCount - successCount}`);

if (successCount === totalCount) {
  console.log('\n✅ All routes successfully updated with CSRF protection!');
} else {
  console.log('\n⚠️  Some routes failed to update. Please check manually.');
}
