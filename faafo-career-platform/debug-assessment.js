const { AlgorithmicAssessmentService } = require('./src/lib/algorithmicAssessmentService');

const mockAssessmentResponse = {
  current_role: 'Software Developer',
  years_experience: '3-5',
  skill_development_interest: ['coding_tech', 'ai_ml', 'mobile_development'],
  career_values: ['growth', 'autonomy', 'impact'],
  work_style_preferences: ['remote', 'collaborative', 'flexible'],
  biggest_obstacles: ['skill_gaps', 'time_constraints'],
  financial_readiness: 4,
  support_level: 3,
  risk_tolerance: 3,
  urgency_level: 4,
  skills_confidence: 75,
  desired_outcomes_work_life: 'Better work-life balance',
  desired_outcomes_financial: 'Higher salary',
  desired_outcomes_personal: 'Career growth',
  location: 'San Francisco, CA'
};

const mockInsights = {
  scores: {
    financialReadiness: 4,
    supportLevel: 3,
    riskTolerance: 3,
    urgencyLevel: 4,
    skillsConfidence: 75,
    readinessScore: 70
  },
  primaryMotivation: 'Career advancement',
  topSkills: ['technical_programming', 'data_analysis', 'project_management'],
  biggestObstacles: ['skill_gaps', 'time_constraints'],
  recommendedTimeline: '6-12 months',
  keyRecommendations: ['Focus on AI/ML skills', 'Build portfolio projects'],
  careerPathSuggestions: [],
  careerPathAnalysis: [],
  overallSkillGaps: [],
  learningPriorities: ['technical_programming', 'data_analysis', 'project_management'],
  estimatedTransitionTime: '6-12 months'
};

async function debug() {
  try {
    console.log('Initializing service...');
    await AlgorithmicAssessmentService.initialize();
    
    console.log('Generating recommendations...');
    const recommendations = await AlgorithmicAssessmentService.generateCareerRecommendations(
      mockAssessmentResponse,
      mockInsights
    );
    
    console.log('Recommendations:', recommendations.length);
    console.log('First recommendation:', recommendations[0]);
  } catch (error) {
    console.error('Error:', error);
  }
}

debug();
