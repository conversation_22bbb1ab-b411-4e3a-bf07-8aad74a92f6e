# 🚀 Enhanced Testerat Installation Guide

## Quick Installation

### 1. Install Python Package
```bash
# Install from source (recommended for latest fixes)
git clone https://github.com/testerat/enhanced.git
cd enhanced/testerat_enhanced
pip install -r requirements.txt
pip install -e .

# Or install from PyPI (when available)
pip install testerat-enhanced
```

### 2. Install Playwright Browsers
```bash
# Install Playwright browsers (required)
playwright install

# Or install specific browsers only
playwright install chromium firefox webkit
```

### 3. Verify Installation
```bash
# Test CLI interface
testerat --version

# Run example test
python -m testerat_enhanced.cli https://example.com "Installation Test"
```

## Detailed Installation

### Prerequisites
- **Python 3.8+** (3.10+ recommended)
- **Node.js 16+** (for Playwright)
- **Git** (for source installation)

### Step-by-Step Installation

#### 1. Create Virtual Environment (Recommended)
```bash
# Create virtual environment
python -m venv testerat_env

# Activate virtual environment
# On Windows:
testerat_env\Scripts\activate
# On macOS/Linux:
source testerat_env/bin/activate
```

#### 2. Install Enhanced Testerat
```bash
# Clone repository
git clone https://github.com/testerat/enhanced.git
cd enhanced/testerat_enhanced

# Install dependencies
pip install -r requirements.txt

# Install in development mode
pip install -e .
```

#### 3. Install Browser Dependencies
```bash
# Install all Playwright browsers
playwright install

# Install system dependencies (Linux only)
sudo playwright install-deps
```

#### 4. Verify Installation
```bash
# Check version
testerat --version
# Should output: Enhanced Testerat 2.0.0

# Test basic functionality
python -c "from testerat_enhanced import EnhancedTesterat; print('✅ Import successful')"

# Run quick test
testerat https://httpbin.org/html "Installation Verification"
```

## Configuration

### Environment Variables
```bash
# Optional: Set default configuration
export TESTERAT_HEADLESS=true
export TESTERAT_OUTPUT_DIR=./reports
export TESTERAT_VERBOSE=false
```

### Configuration File
Create `testerat_config.json`:
```json
{
  "headless": true,
  "detailed_logging": false,
  "test_authentication": true,
  "test_workflows": true,
  "test_api_interactions": true,
  "test_security": true,
  "test_accessibility": true,
  "test_performance": true,
  "browser_timeout": 30000,
  "step_timeout": 10000
}
```

## Usage Examples

### Basic Usage
```bash
# Test any web application
testerat https://example.com

# Test with custom description
testerat https://myapp.com "My Application Testing"

# Test with framework optimization
testerat https://myapp.com --framework react

# Test with custom configuration
testerat https://myapp.com --config testerat_config.json
```

### Advanced Usage
```bash
# Visible browser mode
testerat https://myapp.com --no-headless

# Skip specific test types
testerat https://myapp.com --skip-auth --skip-workflows

# Verbose logging
testerat https://myapp.com --verbose

# Custom output directory
testerat https://myapp.com --output-dir ./my_reports
```

### Programmatic Usage
```python
from testerat_enhanced import EnhancedTesterat, UniversalTestConfig

# Create configuration
config = UniversalTestConfig()
config.test_authentication = True
config.test_workflows = True

# Initialize testerat
testerat = EnhancedTesterat(config)

# Run testing
results = testerat.run_comprehensive_test("https://myapp.com")

# Check results
if results['critical_issues']:
    print("Critical issues found!")
    for issue in results['critical_issues']:
        print(f"- {issue['test_name']}: {issue['details']}")
```

## Troubleshooting

### Common Issues

#### 1. Playwright Installation Issues
```bash
# If browsers fail to install
playwright install --force

# If system dependencies missing (Linux)
sudo playwright install-deps
```

#### 2. Permission Issues
```bash
# If permission denied
sudo chown -R $USER:$USER ~/.cache/ms-playwright
```

#### 3. Import Errors
```bash
# If imports fail, reinstall in development mode
pip uninstall testerat-enhanced
pip install -e .
```

#### 4. Browser Launch Issues
```bash
# Test browser installation
python -c "from playwright.sync_api import sync_playwright; p = sync_playwright().start(); browser = p.chromium.launch(); print('✅ Browser works'); browser.close(); p.stop()"
```

### System Requirements

#### Minimum Requirements
- **RAM**: 4GB (8GB recommended)
- **Storage**: 2GB free space
- **CPU**: 2 cores (4 cores recommended)
- **Network**: Internet connection for browser downloads

#### Supported Platforms
- ✅ **Windows 10/11**
- ✅ **macOS 10.15+**
- ✅ **Ubuntu 18.04+**
- ✅ **Debian 10+**
- ✅ **CentOS 8+**
- ✅ **Docker containers**

## Docker Installation

### Using Docker
```bash
# Build Docker image
docker build -t testerat-enhanced .

# Run tests in container
docker run --rm testerat-enhanced https://example.com
```

### Docker Compose
```yaml
version: '3.8'
services:
  testerat:
    build: .
    volumes:
      - ./reports:/app/reports
    command: ["https://example.com", "Docker Test"]
```

## Development Installation

### For Contributors
```bash
# Clone with development dependencies
git clone https://github.com/testerat/enhanced.git
cd enhanced/testerat_enhanced

# Install with development extras
pip install -e ".[dev]"

# Install pre-commit hooks
pre-commit install

# Run tests
pytest tests/

# Run validation
python validate_fixes.py
```

## Support

### Getting Help
- 📖 **Documentation**: [testerat.dev/docs](https://testerat.dev/docs)
- 🐛 **Issues**: [GitHub Issues](https://github.com/testerat/enhanced/issues)
- 💬 **Discussions**: [GitHub Discussions](https://github.com/testerat/enhanced/discussions)
- 📧 **Email**: <EMAIL>

### Reporting Issues
When reporting issues, please include:
1. Operating system and version
2. Python version (`python --version`)
3. Enhanced Testerat version (`testerat --version`)
4. Full error message and traceback
5. Steps to reproduce the issue

---

**Enhanced Testerat v2.0.0** - Universal Web Testing Framework  
Ready to catch real production issues! 🎯
