# Resume Builder User Guide

## Getting Started

The Resume Builder is a powerful tool that helps you create professional, ATS-friendly resumes quickly and easily. This guide will walk you through all the features and help you build an outstanding resume.

## Accessing the Resume Builder

1. **Log in** to your FAAFO Career Platform account
2. Navigate to **Tools** in the main menu
3. Click on **Resume Builder**
4. You'll see your resume dashboard with all your saved resumes

## Creating Your First Resume

### Step 1: Start a New Resume

1. Click the **"Create New Resume"** button
2. You'll be taken to the resume builder interface
3. The builder opens with the **Personal Information** tab active

### Step 2: Enter Personal Information

Fill out your basic contact information:

- **First Name** and **Last Name** (required)
- **Email Address** (required) - This will be pre-filled with your account email
- **Phone Number** (optional but recommended)
- **Location** (optional) - City, State format (e.g., "San Francisco, CA")
- **Website** (optional) - Your personal website or portfolio
- **LinkedIn Profile** (optional) - Your LinkedIn profile URL

**Tips:**
- Use a professional email address
- Include your phone number for easy contact
- Keep location general (city/state) for privacy

### Step 3: Write Your Professional Summary

1. Scroll down to the **Professional Summary** section
2. Write a brief 2-3 sentence overview of your experience and career goals
3. Focus on your key strengths and what you bring to employers

**Example:**
> "Experienced software engineer with 5+ years of experience in full-stack development. Proven track record of building scalable web applications and leading cross-functional teams. Passionate about creating user-friendly solutions that drive business growth."

### Step 4: Add Work Experience

1. Click on the **Experience** tab
2. Click **"Add Experience"** to create your first entry
3. Fill out the form for each position:
   - **Job Title** (required)
   - **Company Name** (required)
   - **Start Date** (required) - Use month/year format
   - **End Date** (leave empty if current position)
   - **Job Description** - Brief overview of your role
   - **Key Achievements** - Specific accomplishments with metrics when possible

**Tips for Experience Section:**
- List positions in reverse chronological order (most recent first)
- Use action verbs (led, developed, implemented, improved)
- Include quantifiable achievements (increased sales by 25%, managed team of 8)
- Keep descriptions concise but impactful

**Example Achievement:**
> "Improved application performance by 40% through code optimization and database restructuring"

### Step 5: Add Education

1. Click on the **Education** tab
2. Click **"Add Education"** for each degree or certification
3. Fill out the details:
   - **Institution** (required)
   - **Degree** (required)
   - **Field of Study** (optional)
   - **Start Date** and **End Date** (optional)
   - **GPA** (optional - include if 3.5 or higher)
   - **Honors/Awards** (optional)

**Tips:**
- List education in reverse chronological order
- Include relevant coursework for recent graduates
- Add honors, awards, or relevant activities

### Step 6: Add Skills

1. Click on the **Skills** tab
2. Use the **"Add New Skill"** section to add skills one by one
3. For each skill, specify:
   - **Skill Name** (e.g., "JavaScript", "Project Management")
   - **Proficiency Level** (Beginner, Intermediate, Advanced, Expert)
   - **Category** (Programming Languages, Tools, Soft Skills, etc.)

**Skill Categories:**
- Programming Languages
- Frameworks & Libraries
- Databases
- Tools & Technologies
- Soft Skills
- Languages
- Certifications
- Other

**Tips:**
- Be honest about your skill levels
- Include both technical and soft skills
- Organize skills by category for better readability
- Focus on skills relevant to your target jobs

## Customizing Your Resume

### Choosing a Template

1. In the **Resume Details** section, find the **Template** dropdown
2. Choose from available templates:
   - **Modern** - Clean, contemporary design
   - **Classic** - Traditional, professional layout
   - **Minimal** - Simple, clean design
   - **Creative** - Unique, eye-catching layout

3. Your resume preview will update automatically

### Resume Title

Give your resume a descriptive title to help you organize multiple versions:
- "Software Engineer Resume - Tech Companies"
- "Marketing Manager Resume - Healthcare"
- "Entry Level Data Analyst Resume"

## Using the Preview Feature

### Quick Preview Panel
- The right sidebar shows a condensed preview of your resume
- See key statistics (number of experiences, education entries, skills)
- Check your current template and privacy settings

### Full Preview Mode
1. Click the **"Preview"** button in the top toolbar
2. See your complete resume as it will appear to employers
3. Check formatting, layout, and content flow
4. Click **"Edit"** to return to the builder

## Saving Your Resume

### Auto-Save
- Your resume is automatically saved as you make changes
- No need to manually save - your progress is always preserved

### Manual Save
- Click the **"Save"** button to force an immediate save
- Useful before closing the browser or switching away

## Managing Multiple Resumes

### Resume Dashboard
- View all your created resumes
- See creation date, last modified date, and export count
- Quick actions: Edit, Preview, Download, Delete

### Resume Statistics
- Total number of resumes
- Public vs. private resumes
- Total exports across all resumes
- Number of templates used

### Organizing Resumes
- Use descriptive titles to identify different versions
- Create targeted resumes for different industries or roles
- Keep resumes updated with your latest experience

## Best Practices

### Content Guidelines

1. **Keep it Concise**
   - Aim for 1-2 pages maximum
   - Use bullet points for easy scanning
   - Prioritize most relevant information

2. **Use Action Verbs**
   - Start bullet points with strong action verbs
   - Examples: achieved, developed, led, implemented, optimized

3. **Quantify Achievements**
   - Include numbers, percentages, and metrics
   - Show impact and results of your work

4. **Tailor for Each Job**
   - Customize your resume for specific positions
   - Highlight relevant skills and experience
   - Use keywords from job descriptions

### Formatting Tips

1. **Consistency**
   - Use consistent formatting throughout
   - Maintain the same date format
   - Keep bullet point styles uniform

2. **Readability**
   - Use clear, professional fonts
   - Ensure adequate white space
   - Make important information stand out

3. **ATS-Friendly**
   - Use standard section headings
   - Avoid complex formatting or graphics
   - Include relevant keywords naturally

## Troubleshooting

### Common Issues

**Resume Not Saving**
- Check your internet connection
- Ensure you're still logged in
- Try refreshing the page and re-entering changes

**Template Not Loading**
- Clear your browser cache
- Try a different browser
- Check if JavaScript is enabled

**Missing Information**
- Check all required fields are filled
- Verify email format is correct
- Ensure dates are in the correct format

### Getting Help

If you encounter issues:
1. Check this user guide for solutions
2. Visit the Help section for FAQs
3. Contact support through the platform

## Privacy and Sharing

### Privacy Settings
- Resumes are **private by default**
- Only you can see and edit your resumes
- Option to make resumes public (future feature)

### Data Security
- All resume data is encrypted and secure
- Regular backups ensure your data is safe
- You can delete resumes at any time

## Tips for Success

### Before You Start
- Gather all your employment information
- Prepare a list of your key achievements
- Research job descriptions for target roles
- Have your education details ready

### While Building
- Take breaks to review and refine content
- Ask others to review your resume
- Test different templates to see what works best
- Keep updating as you gain new experience

### After Completion
- Proofread carefully for typos and errors
- Test how it looks when printed
- Save multiple versions for different job types
- Keep your resume updated regularly

## Next Steps

Once your resume is complete:
1. **Export** your resume in your preferred format
2. **Practice** your interview skills using our Interview Practice tool
3. **Track** your job applications and progress
4. **Update** your resume regularly with new achievements

Remember, a great resume is just the first step in your job search journey. Use it alongside other FAAFO Career Platform tools to maximize your success!

## Frequently Asked Questions

**Q: How many resumes can I create?**
A: There's no limit to the number of resumes you can create.

**Q: Can I duplicate an existing resume?**
A: Currently, you need to create each resume from scratch, but duplication features are planned for future updates.

**Q: What file formats can I export to?**
A: Export functionality is coming soon and will support PDF, DOCX, and TXT formats.

**Q: Can I share my resume with others?**
A: Sharing features are planned for future releases.

**Q: Is my resume data secure?**
A: Yes, all data is encrypted and stored securely. Only you have access to your resumes.
