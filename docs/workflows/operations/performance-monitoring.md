---
title: "Performance Monitoring & Optimization"
category: "operations"
tags: ["performance", "monitoring", "optimization", "scaling"]
last_updated: "2025-06-15"
last_validated: "2025-06-15"
dependencies: []
used_by: []
maintainer: "performance-team"
ai_context: "Performance monitoring and optimization procedures for FAAFO Career Platform"
---

# Performance Monitoring & Optimization

## Key Performance Metrics

### Frontend Performance
```javascript
// Core Web Vitals
- Largest Contentful Paint (LCP): < 2.5s
- First Input Delay (FID): < 100ms
- Cumulative Layout Shift (CLS): < 0.1
- First Contentful Paint (FCP): < 1.8s
- Time to Interactive (TTI): < 3.8s
```

### Backend Performance
```bash
# API Response Times
- Authentication: < 200ms
- Assessment endpoints: < 500ms
- Career path queries: < 300ms
- Forum operations: < 400ms
- Database queries: < 100ms
```

### Database Performance
```sql
-- Query performance monitoring
SELECT query, mean_exec_time, calls, total_exec_time
FROM pg_stat_statements
ORDER BY mean_exec_time DESC
LIMIT 10;

-- Connection monitoring
SELECT count(*) as active_connections
FROM pg_stat_activity
WHERE state = 'active';
```

## Monitoring Tools Setup

### Vercel Analytics
```typescript
// Enable analytics in next.config.js
const nextConfig = {
  experimental: {
    instrumentationHook: true,
  },
  analytics: {
    id: process.env.VERCEL_ANALYTICS_ID,
  },
};
```

### Custom Performance Monitoring
```typescript
// Performance tracking middleware
export function performanceMiddleware(req: NextRequest) {
  const start = Date.now();
  
  return NextResponse.next().then(response => {
    const duration = Date.now() - start;
    
    // Log slow requests
    if (duration > 1000) {
      console.warn(`Slow request: ${req.url} took ${duration}ms`);
    }
    
    // Add performance headers
    response.headers.set('X-Response-Time', `${duration}ms`);
    return response;
  });
}
```

## Performance Optimization

### Frontend Optimization
```typescript
// Dynamic imports for heavy components
const AssessmentChart = dynamic(() => import('./AssessmentChart'), {
  loading: () => <div>Loading chart...</div>,
  ssr: false
});

// Next.js Image component with optimization
import Image from 'next/image';

<Image
  src="/career-path-image.jpg"
  alt="Career Path"
  width={800}
  height={600}
  priority={false}
  placeholder="blur"
/>
```

### Backend Optimization
```sql
-- Add indexes for frequently queried columns
CREATE INDEX CONCURRENTLY idx_user_email ON "User"(email);
CREATE INDEX CONCURRENTLY idx_assessment_user_id ON "Assessment"(userId);
CREATE INDEX CONCURRENTLY idx_forum_post_created_at ON "ForumPost"(createdAt);
```

### API Response Optimization
```typescript
// Pagination for large datasets
export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const page = parseInt(searchParams.get('page') || '1');
  const limit = Math.min(parseInt(searchParams.get('limit') || '10'), 100);
  const skip = (page - 1) * limit;
  
  const [data, total] = await Promise.all([
    prisma.careerPath.findMany({
      skip,
      take: limit,
      orderBy: { createdAt: 'desc' }
    }),
    prisma.careerPath.count()
  ]);
  
  return NextResponse.json({
    data,
    pagination: {
      page,
      limit,
      total,
      totalPages: Math.ceil(total / limit)
    }
  });
}
```

## Scaling Strategies

### Horizontal Scaling
```typescript
// Edge function for global performance
export const config = {
  runtime: 'edge',
};

export default function handler(req: NextRequest) {
  // Runs at the edge, closer to users
  return new Response('Hello from the edge!');
}
```

### Database Scaling
```bash
# Monitor database performance
SELECT * FROM pg_stat_database WHERE datname = 'faafo_production';

# Check for slow queries
SELECT query, mean_exec_time, calls
FROM pg_stat_statements
WHERE mean_exec_time > 1000
ORDER BY mean_exec_time DESC;
```

## Load Testing

### Artillery Load Testing
```yaml
# artillery-config.yml
config:
  target: 'https://faafo-career-platform.vercel.app'
  phases:
    - duration: 60
      arrivalRate: 10
    - duration: 120
      arrivalRate: 50
    - duration: 60
      arrivalRate: 100

scenarios:
  - name: "User Journey"
    flow:
      - get:
          url: "/"
      - post:
          url: "/api/auth/login"
          json:
            email: "<EMAIL>"
            password: "password"
      - get:
          url: "/api/career-paths"
```

### Performance Testing Script
```bash
#!/bin/bash
# performance-test.sh

echo "Starting performance tests..."

# Load testing
artillery run artillery-config.yml --output report.json

# Generate HTML report
artillery report report.json --output performance-report.html

# Database performance check
psql $DATABASE_URL -c "SELECT * FROM pg_stat_statements ORDER BY mean_exec_time DESC LIMIT 10;"

echo "Performance tests completed. Check performance-report.html"
```

## Performance Alerts

### Alert Thresholds
```typescript
// Alert configuration
const ALERT_THRESHOLDS = {
  API_RESPONSE_TIME: 1000, // 1 second
  DATABASE_QUERY_TIME: 500, // 500ms
  PAGE_LOAD_TIME: 3000, // 3 seconds
  ERROR_RATE: 0.05, // 5%
};
```

### Automated Monitoring
```bash
# Health check script
#!/bin/bash

RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' https://faafo-career-platform.vercel.app/api/health)

if (( $(echo "$RESPONSE_TIME > 1.0" | bc -l) )); then
  echo "ALERT: API response time is ${RESPONSE_TIME}s"
  # Send alert notification
fi
```

## Optimization Checklist

### Frontend Optimization
- [ ] Enable code splitting and lazy loading
- [ ] Optimize images with Next.js Image component
- [ ] Implement proper caching strategies
- [ ] Minimize bundle size
- [ ] Use CDN for static assets
- [ ] Enable compression (gzip/brotli)
- [ ] Optimize Core Web Vitals

### Backend Optimization
- [ ] Add database indexes for frequent queries
- [ ] Implement connection pooling
- [ ] Use pagination for large datasets
- [ ] Cache frequently accessed data
- [ ] Optimize API response sizes
- [ ] Implement rate limiting
- [ ] Monitor and optimize slow queries

### Infrastructure Optimization
- [ ] Use edge functions for global performance
- [ ] Set up proper monitoring and alerting
- [ ] Configure auto-scaling policies
- [ ] Optimize database configuration
- [ ] Use appropriate instance sizes
- [ ] Implement disaster recovery procedures
