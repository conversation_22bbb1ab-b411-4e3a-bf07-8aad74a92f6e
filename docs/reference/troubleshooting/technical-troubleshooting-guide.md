---
title: "Technical Troubleshooting Guide"
category: "reference"
subcategory: "troubleshooting"
tags: ["troubleshooting", "debugging", "technical", "reference"]
last_updated: "2025-06-28"
last_validated: "2025-06-28"
generated_date: "2025-06-28"
generator: "development-team"
dependencies: []
used_by: ["docs/workflows/debugging.md"]
maintainer: "development-team"
ai_context: "Comprehensive technical troubleshooting guide for common development and deployment issues"
---

# Technical Troubleshooting Guide

> **Document Type**: Technical Reference  
> **Date**: 2025-01-20  
> **Audience**: AI Agents & Developers  
> **Next.js Version**: 14.2.5

## 🔍 Diagnostic Framework

### Problem Classification System

#### Level 1: Critical (App Won't Start)
- React rendering errors
- Missing critical imports
- TypeScript compilation failures
- Build process failures

#### Level 2: Functional (App Starts, Features Broken)
- Component functionality issues
- asChild patterns not working
- Navigation problems
- Page-specific errors

#### Level 3: Cosmetic (App Works, UX Issues)
- Styling problems
- Unnecessary UI elements
- Performance warnings
- Non-critical console errors

## 🛠 Systematic Debugging Process

### Phase 1: Initial Assessment (5 minutes)

#### Quick Health Check
```bash
# 1. Check if app starts
npm run dev

# 2. Check build status  
npm run build

# 3. Check for obvious errors
grep -r "Error\|Failed" . --include="*.log" --include="*.txt"
```

#### Common Error Patterns
```typescript
// Pattern 1: Missing Slot import
"Cannot find name 'Slot'" 
→ Add: import { Slot } from "@radix-ui/react-slot"

// Pattern 2: Suspense boundary missing
"useSearchParams() should be wrapped in a suspense boundary"
→ Wrap component in <Suspense>

// Pattern 3: Dynamic server usage
"couldn't be rendered statically because it used `headers`"
→ Add: export const dynamic = 'force-dynamic'

// Pattern 4: TypeScript cloneElement issues
"Object literal may only specify known properties"
→ Fix typing: React.ReactElement<any>
```

### Phase 2: Component Analysis (10 minutes)

#### Critical Components to Check
```typescript
// 1. Badge Component
File: src/components/ui/badge.tsx
Check: Slot import, asChild implementation
Fix: import { Slot } from "@radix-ui/react-slot"

// 2. Button Component  
File: src/components/ui/button.tsx
Check: asChild functionality working
Verify: Comp = asChild ? Slot : "button"

// 3. CollapsibleTrigger
File: src/components/ui/collapsible.tsx
Check: React.cloneElement typing
Fix: Use React.ReactElement<any> typing

// 4. Navigation Bar
File: src/components/layout/NavigationBar.tsx
Check: No "Authenticated" text displayed
Remove: AuthStatusIndicator component
```

#### Page-Level Components
```typescript
// 1. Dashboard Page
File: src/app/dashboard/page.tsx
Check: useSearchParams wrapped in Suspense
Pattern: <Suspense><ComponentWithSearchParams /></Suspense>

// 2. Resume Builder Page
File: src/app/resume-builder/page.tsx  
Check: useSearchParams wrapped in Suspense
Pattern: Same as dashboard

// 3. Error Pages (404/500)
File: src/app/not-found.tsx, src/app/error.tsx
Check: Simple components only, no complex React patterns
Add: export const dynamic = 'force-dynamic' if needed
```

### Phase 3: Build & Production Issues (15 minutes)

#### Build Error Resolution
```bash
# Common build fixes
npm run build 2>&1 | tee build.log

# Check for specific patterns:
grep "Error:" build.log
grep "Failed to compile" build.log
grep "Module not found" build.log
```

#### Production Optimization
```typescript
// 1. Add Suspense boundaries for dynamic imports
import { Suspense } from 'react'

export default function Page() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <DynamicComponent />
    </Suspense>
  )
}

// 2. Force dynamic rendering when needed
export const dynamic = 'force-dynamic'

// 3. Add revalidation for static pages
export const revalidate = 3600 // 1 hour
```

## 🎯 Specific Issue Solutions

### React Component Issues

#### Issue: "Cannot find name 'Slot'"
```typescript
// Problem: Missing import
import { Slot } from "@radix-ui/react-slot"

// Solution: Add to component file
const Button = React.forwardRef<HTMLButtonElement, ButtonProps>(
  ({ className, variant, size, asChild = false, ...props }, ref) => {
    const Comp = asChild ? Slot : "button"
    return (
      <Comp
        className={cn(buttonVariants({ variant, size, className }))}
        ref={ref}
        {...props}
      />
    )
  }
)
```

#### Issue: "asChild pattern not working"
```typescript
// Problem: Incorrect implementation
// Wrong:
<Button asChild>
  <Link href="/dashboard">Dashboard</Link>
</Button>

// Correct:
const Button = ({ asChild = false, ...props }) => {
  const Comp = asChild ? Slot : "button"
  return <Comp {...props} />
}
```

#### Issue: "useSearchParams() should be wrapped in a suspense boundary"
```typescript
// Problem: Missing Suspense wrapper
// Solution:
import { Suspense } from 'react'

function SearchComponent() {
  const searchParams = useSearchParams()
  return <div>{/* component content */}</div>
}

export default function Page() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <SearchComponent />
    </Suspense>
  )
}
```

### Build & Deployment Issues

#### Issue: "Page has an invalid default export"
```typescript
// Problem: Complex components in error pages
// Solution: Simplify error pages
export default function NotFound() {
  return (
    <div className="flex flex-col items-center justify-center min-h-screen">
      <h1 className="text-4xl font-bold mb-4">404</h1>
      <p className="text-lg text-gray-600 mb-8">Page not found</p>
      <a href="/" className="text-blue-600 hover:underline">
        Return Home
      </a>
    </div>
  )
}

// Add if needed:
export const dynamic = 'force-dynamic'
```

#### Issue: "Module not found: Can't resolve"
```bash
# Check if dependency is installed
npm list @radix-ui/react-slot

# If missing, install:
npm install @radix-ui/react-slot

# Clear cache and rebuild:
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

### Performance & UX Issues

#### Issue: "Authenticated" text showing in navigation
```typescript
// Problem: AuthStatusIndicator component
// Solution: Remove from NavigationBar.tsx
// Remove this line:
<AuthStatusIndicator />

// Keep only essential navigation elements
```

#### Issue: Slow page loads
```typescript
// Solution: Add proper loading states
import { Suspense } from 'react'

export default function Layout({ children }) {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-gray-900"></div>
      </div>
    }>
      {children}
    </Suspense>
  )
}
```

## 🚨 Emergency Procedures

### Critical System Failure
```bash
# 1. Immediate rollback to last working state
git log --oneline -10
git reset --hard <last-working-commit>

# 2. Quick health check
npm run dev
npm run build

# 3. If still broken, nuclear option:
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

### Component Emergency Fixes
```typescript
// Emergency Button fix (minimal functionality)
const Button = ({ children, onClick, className, ...props }) => {
  return (
    <button 
      onClick={onClick}
      className={`px-4 py-2 rounded ${className}`}
      {...props}
    >
      {children}
    </button>
  )
}

// Emergency Badge fix (minimal functionality)  
const Badge = ({ children, className, ...props }) => {
  return (
    <span 
      className={`px-2 py-1 text-xs rounded ${className}`}
      {...props}
    >
      {children}
    </span>
  )
}
```

## 📋 Verification Checklist

### Pre-Deployment Checklist
- [ ] `npm run dev` starts without errors
- [ ] `npm run build` completes successfully
- [ ] All pages load correctly
- [ ] Navigation works properly
- [ ] No console errors in browser
- [ ] asChild patterns work correctly
- [ ] Authentication flow works
- [ ] Mobile responsiveness verified

### Component Functionality Checklist
- [ ] Button: All variants work, asChild functional
- [ ] Badge: All variants work, asChild functional
- [ ] CollapsibleTrigger: Expand/collapse works
- [ ] Navigation: All links work, no "Authenticated" text
- [ ] Forms: All form components functional
- [ ] Error pages: 404/500 display correctly

### Performance Checklist
- [ ] First Load JS < 100kB
- [ ] Page load time < 3 seconds
- [ ] No hydration mismatches
- [ ] No memory leaks
- [ ] Proper loading states

## 🔧 Tools & Commands

### Debugging Commands
```bash
# Check component imports
grep -r "import.*Slot" src/

# Find asChild usage
grep -r "asChild" src/

# Check for Suspense usage
grep -r "Suspense" src/

# Find useSearchParams usage
grep -r "useSearchParams" src/

# Check build output
npm run build 2>&1 | grep -E "(Error|Warning|Failed)"
```

### Quick Fixes
```bash
# Fix common import issues
find src/ -name "*.tsx" -exec sed -i 's/import { Slot }/import { Slot } from "@radix-ui\/react-slot"/g' {} \;

# Add Suspense wrapper template
echo 'import { Suspense } from "react"' > suspense-template.txt
```

## 📞 Escalation Path

### When to Escalate
1. **Critical errors persist after 30 minutes**
2. **Build failures with unclear error messages**
3. **Multiple component failures simultaneously**
4. **Production deployment failures**

### Escalation Information to Provide
- Error messages (full stack traces)
- Steps taken to reproduce
- Git commit hash of last working state
- Build logs and console outputs
- Browser/environment information
