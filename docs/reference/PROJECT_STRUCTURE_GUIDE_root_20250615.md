---
title: "FAAFO Project Structure Guide"
category: "reference"
subcategory: "structure"
tags: ["project", "structure", "guide", "organization", "conventions"]
last_updated: "2025-06-15"
last_validated: "2025-06-28"
dependencies: ["STYLE_GUIDE_root_20250615.md"]
used_by: ["PROJECT_CONVENTIONS_root_20250615.md"]
maintainer: "architecture-team"
ai_context: "Project structure guide for FAAFO Career Platform organization"
generated_date: "2025-06-15"
generator: "architecture-team"
---

# 📁 FAAFO Project Structure Guide

## 🎯 Overview

This document serves as the definitive guide for file organization and placement within the FAAFO Career Platform project. It establishes clear conventions to prevent inconsistent file placement and maintain a clean, logical project structure.

## 📂 Directory Structure

### **Root Level**
```
/
├── README.md                    # Main project overview and quick start
├── DOCUMENTATION_INDEX.md       # Central documentation index
├── docs/                        # All project documentation (centralized)
├── faafo-career-platform/       # Main application directory
├── scripts/                     # Project-wide utility scripts
└── [config files]              # Root-level configuration files
```

### **Documentation Structure (`docs/`)**
```
docs/
├── README.md                    # Documentation hub overview
├── project-management/          # Project planning and management docs
│   ├── 00_PROJECT_OVERVIEW.md
│   ├── 01_REQUIREMENTS.md
│   ├── 02_ARCHITECTURE.md
│   ├── 03_TECH_SPECS.md
│   ├── 04_UX_GUIDELINES.md
│   ├── 05_DATA_POLICY.md
│   ├── ASSESSMENT_SYSTEM.md
│   ├── GLOSSARY.md
│   └── README.md
├── development/                 # Implementation and development docs
│   ├── PHASE*_IMPLEMENTATION_*.md
│   ├── *_IMPROVEMENTS_SUMMARY.md
│   ├── CODE_QUALITY_*.md
│   └── README.md
├── testing/                     # All testing documentation
│   ├── TESTING_GUIDE.md
│   ├── *_TEST_REPORT.md
│   ├── *_TESTING_*.md
│   └── README.md
├── user-guides/                 # End-user documentation
│   ├── user-guide.md
│   ├── API.md
│   ├── faq-troubleshooting.md
│   └── README.md
└── operations/                  # Deployment and maintenance docs
    ├── deployment.md
    ├── database-backup.md
    ├── maintenance.md
    └── README.md
```

### **Application Structure (`faafo-career-platform/`)**
```
faafo-career-platform/
├── README.md                    # Application-specific documentation
├── src/                         # Source code (Next.js App Router structure)
│   ├── app/                     # Next.js App Router pages and API routes
│   ├── components/              # React components
│   ├── lib/                     # Shared utilities and configurations
│   ├── emails/                  # Email templates
│   └── types/                   # TypeScript type definitions
├── prisma/                      # Database schema and migrations
├── public/                      # Static assets
├── __tests__/                   # Test files
├── scripts/                     # Application-specific scripts
├── docs/                        # Application-specific documentation (minimal)
└── [config files]              # Application configuration files
```

## 🎯 File Placement Rules

### **Documentation Files (.md)**

#### **Root Level Documentation**
- `README.md` - Main project overview (KEEP AT ROOT)
- `DOCUMENTATION_INDEX.md` - Central documentation index (KEEP AT ROOT)

#### **Centralized Documentation (`docs/`)**
- **Project Management**: Requirements, architecture, specifications, policies
- **Development**: Implementation plans, summaries, code quality reports
- **Testing**: Test plans, reports, guides, strategies
- **User Guides**: End-user documentation, API docs, troubleshooting
- **Operations**: Deployment, maintenance, backup procedures

#### **Application Documentation**
- `faafo-career-platform/README.md` - Application-specific setup and usage
- `faafo-career-platform/docs/` - Only for application-specific docs that don't fit in central docs

### **Source Code Files**

#### **Next.js App Router Structure**
- `src/app/` - Pages, layouts, API routes
- `src/components/` - Reusable React components
- `src/lib/` - Utilities, configurations, services
- `src/emails/` - Email templates
- `src/types/` - TypeScript type definitions

#### **Configuration Files**
- Root level: Project-wide configurations (package.json, tsconfig.json, etc.)
- Application level: App-specific configurations (next.config.ts, tailwind.config.ts, etc.)

### **Test Files**
- `__tests__/` - All test files organized by type (unit, integration, e2e)
- Test files should mirror the source structure they're testing

### **Scripts and Utilities**
- Root `scripts/` - Project-wide utility scripts
- `faafo-career-platform/scripts/` - Application-specific scripts

## 🚫 What NOT to Do

### **Avoid These Patterns**
- ❌ Scattering documentation across multiple directories
- ❌ Duplicate configuration files at different levels
- ❌ Mixed placement of similar file types
- ❌ Deep nesting without clear purpose
- ❌ Inconsistent naming conventions

### **Common Anti-Patterns**
- ❌ `project-docs/` AND `docs/` directories
- ❌ Documentation files in application root
- ❌ Test files scattered across multiple locations
- ❌ Configuration files duplicated at different levels

## ✅ Best Practices

### **File Naming Conventions**
- Use descriptive, consistent names
- Use UPPERCASE for important documents (README.md, CHANGELOG.md)
- Use snake_case or kebab-case for multi-word files
- Include version/phase numbers when relevant (PHASE1_*, PHASE2_*)

### **Directory Organization**
- Group related files together
- Use clear, descriptive directory names
- Maintain consistent depth (avoid unnecessary nesting)
- Include README.md in each major directory

### **Documentation Standards**
- All documentation in Markdown format
- Include clear headings and table of contents
- Use consistent formatting and style
- Cross-reference related documents

## 🔧 Validation Checklist

Before adding new files, ask:

1. **Does this file type have an established location?**
2. **Is there already a similar file in the project?**
3. **Does this belong in centralized docs or application-specific docs?**
4. **Am I following the established naming conventions?**
5. **Will this file be easy to find for its intended audience?**

## 📋 Maintenance Guidelines

### **Regular Cleanup Tasks**
- Review and consolidate scattered documentation
- Remove duplicate or outdated files
- Update documentation index when adding new docs
- Ensure all directories have appropriate README files

### **When Adding New Files**
- Follow the established structure
- Update relevant index/navigation files
- Consider if existing files need updates
- Document any new patterns or conventions

---

**Last Updated**: January 2025  
**Maintained By**: Development Team  
**Review Frequency**: Monthly
