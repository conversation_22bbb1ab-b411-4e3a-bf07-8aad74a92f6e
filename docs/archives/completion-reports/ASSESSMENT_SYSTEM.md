# FAAFO Career Assessment System Documentation

## Overview

The FAAFO Career Assessment System is a comprehensive, multi-step questionnaire designed to help users gain deep insights into their career transition readiness, skills, values, and optimal career paths. The system combines sophisticated question design with intelligent scoring algorithms to provide personalized, actionable guidance.

## System Architecture

### Core Components

1. **Assessment Definition** (`src/lib/assessmentDefinition.ts`)
   - TypeScript interfaces for question types and assessment structure
   - 6-step assessment configuration with 20+ questions
   - Flexible question type system supporting multiple choice, scale, and text responses

2. **Scoring Engine** (`src/lib/assessmentScoring.ts`)
   - Multi-dimensional scoring algorithms
   - Personalized insights generation
   - Career path recommendation logic

3. **UI Components** (`src/components/assessment/`)
   - `QuestionWrapper.tsx` - Consistent question presentation
   - `MultipleChoiceQuestion.tsx` - Single and multi-select options
   - `ScaleQuestion.tsx` - 1-5 scale responses with labels
   - `TextQuestion.tsx` - Open-ended text responses with validation
   - `AssessmentResults.tsx` - Professional results presentation

4. **API Endpoints** (`src/app/api/assessment/`)
   - `route.ts` - Assessment CRUD operations
   - `results/route.ts` - Scored results retrieval

5. **Pages**
   - `src/app/assessment/page.tsx` - Main assessment interface
   - `src/app/assessment/results/page.tsx` - Results presentation

## Assessment Structure

### Step 1: Understanding Your Current Situation
**Questions:** 3 | **Focus:** Current employment and dissatisfaction analysis
- Dissatisfaction triggers (12 options, multi-select)
- Employment status (7 options)
- Years of experience (6 ranges)

### Step 2: Defining Your Desired Outcomes
**Questions:** 4 | **Focus:** Goals and priorities for career change
- Financial comfort level (1-5 scale)
- Work-life balance importance (4 options)
- Compensation expectations (5 options)
- Autonomy preferences (1-5 scale)

### Step 3: Skills and Strengths Assessment
**Questions:** 3 | **Focus:** Current capabilities and development interests
- Top professional skills (15 options, multi-select up to 5)
- Skill development interests (13 options, multi-select)
- Learning preferences (8 options, multi-select)

### Step 4: Values and Work Preferences
**Questions:** 4 | **Focus:** Core values and work environment preferences
- Core values (14 options, multi-select up to 5)
- Work environment preferences (7 options)
- Team size preferences (6 options)
- Risk tolerance (1-5 scale)

### Step 5: Career Transition Readiness
**Questions:** 5 | **Focus:** Readiness assessment and obstacle identification
- Transition timeline (6 options)
- Biggest obstacles (12 options, multi-select)
- Support system strength (1-5 scale)
- Primary motivation (10 options)
- Confidence level (1-5 scale)

### Step 6: Additional Insights
**Questions:** 3 | **Focus:** Open-ended personalization
- Ideal work day description (text, 500 chars)
- Career inspiration sources (text, 300 chars)
- Additional thoughts (text, 500 chars)

## Scoring System

### Multi-Dimensional Analysis

The assessment generates scores across multiple dimensions:

#### 1. Overall Readiness Score (0-100)
Weighted combination of:
- Financial readiness (25%)
- Confidence level (25%)
- Support system (20%)
- Risk tolerance (15%)
- Skills confidence (15%)

#### 2. Component Scores (1-5 scales)
- **Financial Readiness:** Direct assessment of financial comfort
- **Risk Tolerance:** Comfort with career-related uncertainty
- **Support Level:** Strength of available support network
- **Urgency Level:** Calculated from timeline and situation
- **Skills Confidence:** Based on number and quality of selected skills (0-100)

### Insights Generation

The system generates personalized insights including:

#### Career Path Suggestions
- Skills-based matching (programming → tech paths)
- Values alignment (creativity → creative services)
- Work preference matching (autonomy → freelancing)
- Default suggestions for comprehensive coverage

#### Personalized Recommendations
- Financial preparation guidance
- Support network building
- Skills development priorities
- Mindset and confidence work
- Direction clarification support

#### Timeline Guidance
- Readiness-based timeline recommendations
- Urgency-adjusted planning horizons
- Preparation phase suggestions

## Technical Implementation

### Question Types

```typescript
interface MCQuestion extends BaseQuestion {
  type: 'multipleChoice';
  options: Option[];
  allowMultiple?: boolean;
  required?: boolean;
}

interface ScQuestion extends BaseQuestion {
  type: 'scale';
  minLabel: string;
  maxLabel: string;
  numberOfSteps: number;
  required?: boolean;
}

interface TextQuestion extends BaseQuestion {
  type: 'text';
  placeholder?: string;
  maxLength?: number;
  minLength?: number;
  required?: boolean;
}
```

### Data Storage

Assessment responses are stored in PostgreSQL with flexible JSON storage:

```sql
model AssessmentResponse {
  id           String     @id @default(uuid())
  assessmentId String
  assessment   Assessment @relation(fields: [assessmentId], references: [id], onDelete: Cascade)
  questionKey  String     -- e.g., "top_skills", "career_change_motivation"
  answerValue  Json       -- Flexible storage for string, string[], number, or null
  createdAt    DateTime   @default(now())
}
```

### Scoring Algorithms

The scoring system uses rule-based algorithms with weighted calculations:

```typescript
// Example: Overall readiness calculation
const readinessScore = Math.round(
  (financialReadiness * 0.25 +
   confidenceLevel * 0.25 +
   supportLevel * 0.2 +
   riskTolerance * 0.15 +
   (skillsConfidence / 100 * 5) * 0.15) * 20
);
```

## User Experience Features

### Progress Management
- Auto-save functionality every 5 seconds
- Step-by-step navigation with validation
- Progress resumption from any point
- Visual progress indicators

### Results Presentation
- Professional visual design with score displays
- Color-coded readiness levels
- Detailed breakdown of all dimensions
- Actionable next steps and recommendations
- Career path suggestions with explanations

### Accessibility
- Keyboard navigation support
- Screen reader compatibility
- High contrast color schemes
- Responsive design for all devices

## Future Enhancements

### Phase 2: Advanced Analytics
- Historical progress tracking
- Assessment retake comparisons
- Trend analysis and insights
- Benchmark comparisons

### Phase 3: AI Integration
- Natural language processing of text responses
- Dynamic question generation
- Personalized coaching suggestions
- Advanced career path matching

### Phase 4: Extended Assessment
- Personality assessment integration
- Skills testing and validation
- Industry-specific questionnaires
- Psychometric assessments

## Maintenance and Updates

### Content Updates
- Regular review of question relevance
- Option expansion based on user feedback
- Career path database updates
- Scoring algorithm refinements

### Technical Maintenance
- Performance monitoring of scoring algorithms
- Database query optimization
- Component testing and validation
- User experience improvements

## Success Metrics

### Completion Metrics
- Assessment completion rate (target: >70%)
- Step-by-step completion analysis
- Question response quality assessment

### Engagement Metrics
- Results page engagement time
- Career path exploration rates
- Assessment retake frequency
- User satisfaction scores

### Accuracy Metrics
- User validation of recommendations
- Career path suggestion relevance
- Scoring accuracy feedback
- Long-term outcome tracking
