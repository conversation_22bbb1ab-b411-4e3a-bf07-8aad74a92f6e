# Quick Debugging Reference Guide

> **Document Type**: Emergency Reference  
> **Date**: 2025-01-20  
> **For**: Future AI Agents  

## 🚨 Emergency Checklist

### React Rendering Errors
```bash
# Check these immediately:
□ Missing @radix-ui/react-slot import in Badge/Button components
□ asChild patterns incorrectly implemented  
□ useSearchParams without Suspense boundary
□ Complex React components in 404/500 pages
□ TypeScript errors in CollapsibleTrigger
```

### Build Failures
```bash
# Common build issues:
□ "Cannot find name 'Slot'" → Add import { Slot } from "@radix-ui/react-slot"
□ "useSearchParams() should be wrapped in suspense" → Add Suspense boundary
□ "Dynamic server usage" → Add export const dynamic = 'force-dynamic'
□ TypeScript cloneElement errors → Fix typing with React.ReactElement<any>
```

## 🔧 Instant Fixes

### 1. Badge Component Missing Slot
```typescript
// File: src/components/ui/badge.tsx
// Add this import at the top:
import { Slot } from "@radix-ui/react-slot"
```

### 2. Suspense Boundary for Search Params
```typescript
// File: src/app/dashboard/page.tsx or src/app/resume-builder/page.tsx
import { Suspense } from 'react'

function ContentComponent() {
  const searchParams = useSearchParams(); // This needs Suspense
  // ... rest of component
}

export default function Page() {
  return (
    <Suspense fallback={<div>Loading...</div>}>
      <ContentComponent />
    </Suspense>
  );
}
```

### 3. Fix Link-Button Patterns
```typescript
// WRONG:
<Link href="/path">
  <Button>Text</Button>
</Link>

// CORRECT:
<Button asChild>
  <Link href="/path">Text</Link>
</Button>
```

### 4. CollapsibleTrigger TypeScript Fix
```typescript
// File: src/components/ui/collapsible.tsx
if (asChild && React.isValidElement(children)) {
  const element = children as React.ReactElement<any>;
  return React.cloneElement(element, {
    ...element.props,
    className: cn(className, element.props.className)
  });
}
```

## 📋 Verification Steps

### After Each Fix
```bash
1. npm run build          # Check if build succeeds
2. npm run dev           # Test in development  
3. Check browser console # Look for React errors
4. Test affected pages   # Verify functionality
```

### Final Verification
```bash
□ All 82 pages build successfully
□ No TypeScript errors
□ All asChild patterns working
□ Navigation functional
□ No "Authenticated" text in nav
□ Resume builder loads correctly
□ Dashboard displays properly
```

## 🎯 Critical File Locations

### Components to Check First
```bash
src/components/ui/badge.tsx           # Slot import
src/components/ui/button.tsx          # asChild pattern
src/components/ui/collapsible.tsx     # TypeScript typing
src/components/layout/NavigationBar.tsx # Remove "Authenticated"
```

### Pages to Verify
```bash
src/app/dashboard/page.tsx            # Suspense boundary
src/app/resume-builder/page.tsx       # Suspense boundary
src/app/not-found.tsx                 # Simple components only
src/app/error.tsx                     # Simple components only
```

## ⚡ Emergency Commands

### Quick Health Check
```bash
# 1. Check if app starts
npm run dev

# 2. Check build status
npm run build

# 3. Find specific errors
grep -r "Cannot find name" src/
grep -r "useSearchParams" src/
```

### Nuclear Reset (Last Resort)
```bash
# If everything is broken:
rm -rf .next node_modules package-lock.json
npm install
npm run build
```

## 🔍 Error Pattern Recognition

### "Cannot find name 'Slot'"
```typescript
// Missing import in Badge or Button component
// Fix: Add import { Slot } from "@radix-ui/react-slot"
```

### "useSearchParams() should be wrapped in a suspense boundary"
```typescript
// Missing Suspense wrapper
// Fix: Wrap component using useSearchParams in <Suspense>
```

### "Page has an invalid default export"
```typescript
// Complex React components in error pages
// Fix: Simplify 404/500 pages, add export const dynamic = 'force-dynamic'
```

### "Object literal may only specify known properties"
```typescript
// TypeScript cloneElement issues
// Fix: Use React.ReactElement<any> typing
```

## 🎨 UI/UX Quick Fixes

### Remove "Authenticated" Text
```typescript
// File: src/components/layout/NavigationBar.tsx
// Remove or comment out:
// <AuthStatusIndicator />
```

### Fix Button Styling
```typescript
// Ensure all button variants work:
<Button variant="default">Default</Button>
<Button variant="destructive">Destructive</Button>
<Button variant="outline">Outline</Button>
<Button variant="secondary">Secondary</Button>
<Button variant="ghost">Ghost</Button>
<Button variant="link">Link</Button>
```

### Fix Badge Styling
```typescript
// Ensure all badge variants work:
<Badge variant="default">Default</Badge>
<Badge variant="secondary">Secondary</Badge>
<Badge variant="destructive">Destructive</Badge>
<Badge variant="outline">Outline</Badge>
```

## 📊 Success Metrics

### Build Success Indicators
```bash
✓ Compiled successfully
✓ Linting and checking validity of types
✓ Collecting page data
✓ Generating static pages (82/82)
✓ Collecting build traces
✓ Finalizing page optimization
```

### Runtime Success Indicators
```bash
□ No console errors in browser
□ All pages load without 404s
□ Navigation works smoothly
□ Forms submit correctly
□ asChild patterns render properly
```

## 🚀 Production Readiness

### Pre-Deployment Checklist
```bash
□ npm run build succeeds
□ All 82 pages generated
□ No TypeScript errors
□ No React warnings
□ Performance metrics acceptable
□ All user flows tested
```

### Performance Targets
```bash
□ First Load JS < 100kB
□ Page load time < 3 seconds
□ No hydration mismatches
□ Lighthouse score > 90
```

## 📞 When to Escalate

### Escalate Immediately If:
- Build fails after following all fixes
- More than 3 components broken simultaneously
- TypeScript errors persist after type fixes
- Production deployment fails
- Performance degrades significantly

### Information to Provide:
- Exact error messages
- Steps already attempted
- Git commit hash
- Build logs
- Browser console output

## 💡 Pro Tips

### Development Workflow
1. **Always test build after changes**: `npm run build`
2. **Check browser console**: Look for React warnings
3. **Test asChild patterns**: Verify polymorphic behavior
4. **Verify TypeScript**: No red squiggles in IDE
5. **Test on mobile**: Responsive design verification

### Common Gotchas
- Don't put complex components in error pages
- Always wrap useSearchParams in Suspense
- Import Slot from correct package
- Use proper TypeScript typing for cloneElement
- Test asChild patterns with different elements

### Best Practices
- Keep error pages simple
- Use Suspense boundaries liberally
- Import dependencies explicitly
- Test build frequently
- Verify all component variants work
