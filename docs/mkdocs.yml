site_name: FAAFO Career Platform Documentation
site_description: AI-optimized documentation for the FAAFO Career Platform
site_url: https://docs.faafo.dev
repo_url: https://github.com/dm601990/faafo
repo_name: dm601990/faafo

# Documentation structure
nav:
  - Home: index.md
  - Getting Started:
    - Quick Start: workflows/quick-start.md
    - Development Setup: workflows/development-setup.md
    - Testing Guide: workflows/testing.md
  - User Guides:
    - User Manual: user-guides/user-guide.md
    - API Reference: user-guides/API.md
    - Troubleshooting: user-guides/faq-troubleshooting.md
  - Development:
    - Architecture: project-management/02_ARCHITECTURE.md
    - Tech Specs: project-management/03_TECH_SPECS.md
    - Assessment System: project-management/ASSESSMENT_SYSTEM.md
  - Operations:
    - Deployment: operations/deployment.md
    - Database Migration: operations/DATABASE_MIGRATION_VERCEL_POSTGRES.md
    - Maintenance: operations/maintenance.md
  - Testing:
    - Testing Strategy: testing/core/testing-strategy.md
    - API Testing: testing/api-testing/
  - Reference:
    - Glossary: project-management/GLOSSARY.md
    - Project Status: project-management/07_PROJECT_STATUS.md

# Theme configuration
theme:
  name: material
  palette:
    # Palette toggle for light mode
    - scheme: default
      primary: blue grey
      accent: deep orange
      toggle:
        icon: material/brightness-7
        name: Switch to dark mode
    # Palette toggle for dark mode
    - scheme: slate
      primary: blue grey
      accent: deep orange
      toggle:
        icon: material/brightness-4
        name: Switch to light mode
  features:
    - navigation.tabs
    - navigation.tabs.sticky
    - navigation.sections
    - navigation.expand
    - navigation.path
    - navigation.indexes
    - toc.follow
    - toc.integrate
    - search.suggest
    - search.highlight
    - search.share
    - content.code.copy
    - content.code.annotate

# Extensions
markdown_extensions:
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - md_in_html
  - toc:
      permalink: true
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_generator: !!python/name:materialx.emoji.to_svg
      emoji_index: !!python/name:materialx.emoji.twemoji
  - pymdownx.highlight:
      anchor_linenums: true
      line_spans: __span
      pygments_lang_class: true
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.magiclink:
      repo_url_shorthand: true
      user: dm601990
      repo: faafo
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

# Plugins
plugins:
  - search:
      separator: '[\s\-,:!=\[\]()"`/]+|\.(?!\d)|&[lg]t;|(?!\b)(?=[A-Z][a-z])'
  - macros:
      include_dir: docs
      include_yaml:
        - docs/data/
  - git-revision-date-localized:
      enable_creation_date: true
  - minify:
      minify_html: true

# Extra configuration
extra:
  version:
    provider: mike
  social:
    - icon: fontawesome/brands/github
      link: https://github.com/dm601990/faafo
  analytics:
    provider: google
    property: !ENV GOOGLE_ANALYTICS_KEY

# Documentation directory
docs_dir: docs

# Build directory
site_dir: site

# Extra CSS and JavaScript
extra_css:
  - stylesheets/extra.css

extra_javascript:
  - javascripts/mathjax.js
  - https://polyfill.io/v3/polyfill.min.js?features=es6
  - https://cdn.jsdelivr.net/npm/mathjax@3/es5/tex-mml-chtml.js

# Validation settings
validation:
  omitted_files: warn
  absolute_links: warn
  unrecognized_links: warn

# Watch additional files
watch:
  - docs/
  - mkdocs.yml
