# Skill Gap Analyzer - Monitoring Guide

## Overview

This guide covers comprehensive monitoring and observability for the Skill Gap Analyzer feature, including metrics, alerts, dashboards, and troubleshooting procedures.

## Monitoring Stack

### Core Components
- **Sentry** - Error tracking and performance monitoring
- **Vercel Analytics** - Application performance metrics
- **Upstash** - Redis monitoring
- **Neon** - Database monitoring
- **Custom Metrics** - Business logic monitoring

### Key Metrics

#### Application Performance
- **Response Time**: API endpoint response times
- **Throughput**: Requests per second
- **Error Rate**: Percentage of failed requests
- **Availability**: Uptime percentage

#### Business Metrics
- **Assessment Completion Rate**: Percentage of started assessments completed
- **AI Analysis Success Rate**: Successful AI analysis generations
- **User Engagement**: Active users and feature usage
- **Recommendation Effectiveness**: User interaction with recommendations

#### Infrastructure Metrics
- **Database Performance**: Query execution times, connection pool usage
- **Cache Hit Rate**: Redis cache effectiveness
- **Memory Usage**: Application memory consumption
- **CPU Utilization**: Server resource usage

## Sentry Configuration

### Error Tracking Setup

```typescript
// sentry.client.config.ts
import * as Sentry from '@sentry/nextjs';

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.SENTRY_ENVIRONMENT,
  tracesSampleRate: 1.0,
  profilesSampleRate: 1.0,
  beforeSend(event, hint) {
    // Filter out sensitive information
    if (event.request?.data) {
      delete event.request.data.password;
      delete event.request.data.token;
    }
    return event;
  },
  integrations: [
    new Sentry.BrowserTracing({
      tracingOrigins: ['localhost', 'faafo.com', /^\//],
    }),
  ],
});
```

### Custom Error Tracking

```typescript
// lib/monitoring/errors.ts
import * as Sentry from '@sentry/nextjs';

export const trackSkillAssessmentError = (error: Error, context: any) => {
  Sentry.withScope((scope) => {
    scope.setTag('feature', 'skill-gap-analyzer');
    scope.setTag('operation', 'assessment-creation');
    scope.setContext('assessment', context);
    Sentry.captureException(error);
  });
};

export const trackAIAnalysisError = (error: Error, userId: string, careerPath: string) => {
  Sentry.withScope((scope) => {
    scope.setTag('feature', 'ai-analysis');
    scope.setUser({ id: userId });
    scope.setContext('analysis', { careerPath });
    Sentry.captureException(error);
  });
};
```

## Performance Monitoring

### API Endpoint Monitoring

```typescript
// lib/monitoring/performance.ts
import { performance } from 'perf_hooks';

export const monitorAPIEndpoint = (endpoint: string) => {
  return (req: NextRequest, res: NextResponse, next: NextFunction) => {
    const start = performance.now();
    
    res.on('finish', () => {
      const duration = performance.now() - start;
      
      // Log performance metrics
      console.log({
        endpoint,
        method: req.method,
        statusCode: res.statusCode,
        duration: Math.round(duration),
        timestamp: new Date().toISOString()
      });
      
      // Send to monitoring service
      if (duration > 1000) {
        Sentry.addBreadcrumb({
          message: 'Slow API response',
          level: 'warning',
          data: { endpoint, duration }
        });
      }
    });
    
    next();
  };
};
```

### Database Query Monitoring

```typescript
// lib/monitoring/database.ts
import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient({
  log: [
    {
      emit: 'event',
      level: 'query',
    },
    {
      emit: 'event',
      level: 'error',
    },
  ],
});

prisma.$on('query', (e) => {
  if (e.duration > 1000) {
    console.warn('Slow query detected:', {
      query: e.query,
      duration: e.duration,
      params: e.params
    });
    
    Sentry.addBreadcrumb({
      message: 'Slow database query',
      level: 'warning',
      data: {
        query: e.query.substring(0, 100),
        duration: e.duration
      }
    });
  }
});

prisma.$on('error', (e) => {
  Sentry.captureException(new Error(`Database error: ${e.message}`));
});
```

## Business Metrics Tracking

### User Engagement Metrics

```typescript
// lib/monitoring/analytics.ts
export const trackUserEngagement = {
  assessmentStarted: (userId: string) => {
    analytics.track('Assessment Started', {
      userId,
      timestamp: new Date().toISOString(),
      feature: 'skill-gap-analyzer'
    });
  },
  
  assessmentCompleted: (userId: string, skillCount: number) => {
    analytics.track('Assessment Completed', {
      userId,
      skillCount,
      timestamp: new Date().toISOString(),
      feature: 'skill-gap-analyzer'
    });
  },
  
  analysisGenerated: (userId: string, careerPath: string, readiness: number) => {
    analytics.track('Analysis Generated', {
      userId,
      careerPath,
      readiness,
      timestamp: new Date().toISOString(),
      feature: 'ai-analysis'
    });
  },
  
  recommendationClicked: (userId: string, recommendationId: string, resourceType: string) => {
    analytics.track('Recommendation Clicked', {
      userId,
      recommendationId,
      resourceType,
      timestamp: new Date().toISOString(),
      feature: 'learning-recommendations'
    });
  }
};
```

### Conversion Funnel Tracking

```typescript
// lib/monitoring/funnel.ts
export const trackConversionFunnel = {
  step1_landingPage: (userId: string) => {
    analytics.track('Funnel: Landing Page View', { userId });
  },
  
  step2_assessmentStart: (userId: string) => {
    analytics.track('Funnel: Assessment Started', { userId });
  },
  
  step3_skillsAdded: (userId: string, skillCount: number) => {
    analytics.track('Funnel: Skills Added', { userId, skillCount });
  },
  
  step4_analysisRequested: (userId: string) => {
    analytics.track('Funnel: Analysis Requested', { userId });
  },
  
  step5_recommendationsViewed: (userId: string) => {
    analytics.track('Funnel: Recommendations Viewed', { userId });
  },
  
  step6_actionTaken: (userId: string, actionType: string) => {
    analytics.track('Funnel: Action Taken', { userId, actionType });
  }
};
```

## Alert Configuration

### Critical Alerts

```yaml
# alerts/critical.yml
alerts:
  - name: "High Error Rate"
    condition: "error_rate > 5%"
    duration: "5m"
    severity: "critical"
    channels: ["pagerduty", "slack-critical"]
    description: "Error rate exceeded 5% for 5 minutes"
    
  - name: "Database Connection Failure"
    condition: "db_connection_errors > 0"
    duration: "1m"
    severity: "critical"
    channels: ["pagerduty", "slack-critical"]
    description: "Database connection failures detected"
    
  - name: "AI Service Unavailable"
    condition: "ai_service_errors > 10"
    duration: "5m"
    severity: "critical"
    channels: ["pagerduty", "slack-critical"]
    description: "AI service experiencing high error rate"
```

### Warning Alerts

```yaml
# alerts/warnings.yml
alerts:
  - name: "Slow Response Time"
    condition: "p95_response_time > 2s"
    duration: "10m"
    severity: "warning"
    channels: ["slack-alerts"]
    description: "95th percentile response time exceeded 2 seconds"
    
  - name: "Low Cache Hit Rate"
    condition: "cache_hit_rate < 80%"
    duration: "15m"
    severity: "warning"
    channels: ["slack-alerts"]
    description: "Redis cache hit rate below 80%"
    
  - name: "High Memory Usage"
    condition: "memory_usage > 85%"
    duration: "10m"
    severity: "warning"
    channels: ["slack-alerts"]
    description: "Memory usage exceeded 85%"
```

## Dashboard Configuration

### Application Dashboard

```json
{
  "dashboard": {
    "title": "Skill Gap Analyzer - Application Metrics",
    "panels": [
      {
        "title": "Request Rate",
        "type": "graph",
        "targets": [
          {
            "expr": "rate(http_requests_total[5m])",
            "legendFormat": "{{endpoint}}"
          }
        ]
      },
      {
        "title": "Response Time",
        "type": "graph",
        "targets": [
          {
            "expr": "histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m]))",
            "legendFormat": "95th percentile"
          }
        ]
      },
      {
        "title": "Error Rate",
        "type": "stat",
        "targets": [
          {
            "expr": "rate(http_requests_total{status=~\"5..\"}[5m]) / rate(http_requests_total[5m]) * 100",
            "legendFormat": "Error Rate %"
          }
        ]
      }
    ]
  }
}
```

### Business Metrics Dashboard

```json
{
  "dashboard": {
    "title": "Skill Gap Analyzer - Business Metrics",
    "panels": [
      {
        "title": "Daily Active Users",
        "type": "stat",
        "targets": [
          {
            "expr": "count(distinct(user_id)) by (day)",
            "legendFormat": "DAU"
          }
        ]
      },
      {
        "title": "Assessment Completion Rate",
        "type": "gauge",
        "targets": [
          {
            "expr": "assessments_completed / assessments_started * 100",
            "legendFormat": "Completion Rate %"
          }
        ]
      },
      {
        "title": "AI Analysis Success Rate",
        "type": "gauge",
        "targets": [
          {
            "expr": "ai_analyses_successful / ai_analyses_total * 100",
            "legendFormat": "Success Rate %"
          }
        ]
      }
    ]
  }
}
```

## Health Checks

### Application Health Check

```typescript
// pages/api/health/skill-gap-analyzer.ts
export default async function handler(req: NextRequest, res: NextResponse) {
  const healthChecks = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    checks: {}
  };
  
  try {
    // Database connectivity
    await prisma.$queryRaw`SELECT 1`;
    healthChecks.checks.database = 'healthy';
  } catch (error) {
    healthChecks.checks.database = 'unhealthy';
    healthChecks.status = 'unhealthy';
  }
  
  try {
    // Redis connectivity
    await redis.ping();
    healthChecks.checks.redis = 'healthy';
  } catch (error) {
    healthChecks.checks.redis = 'unhealthy';
    healthChecks.status = 'unhealthy';
  }
  
  try {
    // AI service connectivity
    await testGeminiConnection();
    healthChecks.checks.aiService = 'healthy';
  } catch (error) {
    healthChecks.checks.aiService = 'unhealthy';
    healthChecks.status = 'degraded';
  }
  
  const statusCode = healthChecks.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(healthChecks);
}
```

### Deep Health Check

```typescript
// pages/api/health/deep.ts
export default async function handler(req: NextRequest, res: NextResponse) {
  const deepChecks = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    checks: {},
    performance: {}
  };
  
  // Test database performance
  const dbStart = performance.now();
  try {
    await prisma.skillAssessment.findFirst();
    deepChecks.performance.database = Math.round(performance.now() - dbStart);
    deepChecks.checks.database = 'healthy';
  } catch (error) {
    deepChecks.checks.database = 'unhealthy';
    deepChecks.status = 'unhealthy';
  }
  
  // Test AI service performance
  const aiStart = performance.now();
  try {
    await generateTestAnalysis();
    deepChecks.performance.aiService = Math.round(performance.now() - aiStart);
    deepChecks.checks.aiService = 'healthy';
  } catch (error) {
    deepChecks.checks.aiService = 'unhealthy';
    deepChecks.status = 'degraded';
  }
  
  // Test cache performance
  const cacheStart = performance.now();
  try {
    await redis.set('health-check', 'test');
    await redis.get('health-check');
    deepChecks.performance.cache = Math.round(performance.now() - cacheStart);
    deepChecks.checks.cache = 'healthy';
  } catch (error) {
    deepChecks.checks.cache = 'unhealthy';
    deepChecks.status = 'degraded';
  }
  
  const statusCode = deepChecks.status === 'healthy' ? 200 : 503;
  res.status(statusCode).json(deepChecks);
}
```

## Log Management

### Structured Logging

```typescript
// lib/monitoring/logger.ts
import winston from 'winston';

const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: winston.format.combine(
    winston.format.timestamp(),
    winston.format.errors({ stack: true }),
    winston.format.json()
  ),
  defaultMeta: {
    service: 'skill-gap-analyzer',
    environment: process.env.NODE_ENV
  },
  transports: [
    new winston.transports.Console(),
    new winston.transports.File({ filename: 'logs/error.log', level: 'error' }),
    new winston.transports.File({ filename: 'logs/combined.log' })
  ]
});

export const logSkillAssessment = (action: string, userId: string, data: any) => {
  logger.info('Skill assessment action', {
    action,
    userId,
    data,
    feature: 'skill-assessment'
  });
};

export const logAIAnalysis = (action: string, userId: string, data: any) => {
  logger.info('AI analysis action', {
    action,
    userId,
    data,
    feature: 'ai-analysis'
  });
};
```

### Log Aggregation

```typescript
// lib/monitoring/logAggregation.ts
export const aggregateLogs = {
  errorsByEndpoint: async (timeRange: string) => {
    const query = `
      SELECT endpoint, COUNT(*) as error_count
      FROM logs
      WHERE level = 'error'
        AND timestamp > NOW() - INTERVAL '${timeRange}'
      GROUP BY endpoint
      ORDER BY error_count DESC
    `;
    return await executeLogQuery(query);
  },
  
  slowQueries: async (threshold: number) => {
    const query = `
      SELECT query, AVG(duration) as avg_duration, COUNT(*) as count
      FROM query_logs
      WHERE duration > ${threshold}
      GROUP BY query
      ORDER BY avg_duration DESC
    `;
    return await executeLogQuery(query);
  },
  
  userActivitySummary: async (userId: string, timeRange: string) => {
    const query = `
      SELECT action, COUNT(*) as count
      FROM user_activity_logs
      WHERE user_id = '${userId}'
        AND timestamp > NOW() - INTERVAL '${timeRange}'
      GROUP BY action
    `;
    return await executeLogQuery(query);
  }
};
```

## Troubleshooting Runbooks

### High Error Rate

```markdown
## High Error Rate Runbook

### Symptoms
- Error rate > 5% for 5+ minutes
- Increased 5xx responses
- User complaints about functionality

### Investigation Steps
1. Check Sentry for recent errors
2. Review application logs for patterns
3. Verify database connectivity
4. Check AI service status
5. Review recent deployments

### Resolution Steps
1. If database issue: Check connection pool, restart if needed
2. If AI service issue: Implement fallback responses
3. If deployment issue: Consider rollback
4. If traffic spike: Scale resources

### Prevention
- Implement circuit breakers
- Add more comprehensive error handling
- Improve monitoring coverage
```

### Slow Response Times

```markdown
## Slow Response Times Runbook

### Symptoms
- P95 response time > 2 seconds
- User complaints about slow loading
- Timeout errors

### Investigation Steps
1. Check database query performance
2. Review cache hit rates
3. Analyze slow query logs
4. Check AI service response times
5. Review resource utilization

### Resolution Steps
1. Optimize slow queries
2. Increase cache TTL for stable data
3. Scale database resources
4. Implement request queuing for AI service
5. Add CDN for static assets

### Prevention
- Regular performance testing
- Query optimization reviews
- Proactive scaling policies
```

## Maintenance Tasks

### Daily Tasks
- [ ] Review error logs and alerts
- [ ] Check system health dashboards
- [ ] Monitor key business metrics
- [ ] Verify backup completion

### Weekly Tasks
- [ ] Analyze performance trends
- [ ] Review slow query reports
- [ ] Update monitoring thresholds
- [ ] Clean up old logs

### Monthly Tasks
- [ ] Performance optimization review
- [ ] Alert effectiveness analysis
- [ ] Monitoring tool updates
- [ ] Capacity planning review

## Monitoring Best Practices

### Alerting Guidelines
1. **Alert on symptoms, not causes**
2. **Make alerts actionable**
3. **Avoid alert fatigue**
4. **Include context in alert messages**
5. **Test alert channels regularly**

### Dashboard Design
1. **Focus on key metrics**
2. **Use appropriate visualizations**
3. **Include business context**
4. **Make dashboards self-explanatory**
5. **Regular dashboard reviews**

### Log Management
1. **Use structured logging**
2. **Include correlation IDs**
3. **Log at appropriate levels**
4. **Sanitize sensitive data**
5. **Implement log retention policies**
