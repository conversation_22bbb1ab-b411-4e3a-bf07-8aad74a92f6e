# Skill Gap Analyzer - Testing Documentation

## Overview

This document outlines the comprehensive testing strategy for the Skill Gap Analyzer feature, including unit tests, integration tests, end-to-end tests, and manual testing procedures.

## Testing Strategy

### Test Coverage Goals
- **Overall Coverage**: 95%+
- **Critical Paths**: 100%
- **API Endpoints**: 100%
- **Business Logic**: 95%+
- **UI Components**: 90%+

### Testing Pyramid
1. **Unit Tests** (70%): Fast, isolated component testing
2. **Integration Tests** (20%): API and database integration
3. **E2E Tests** (10%): Complete user workflows

## Unit Testing

### Test Framework
- **Jest** for test runner and assertions
- **React Testing Library** for component testing
- **MSW** for API mocking
- **@testing-library/user-event** for user interactions

### Component Tests

#### SkillAssessmentForm
```typescript
// Test file: __tests__/components/SkillAssessmentForm.test.tsx

describe('SkillAssessmentForm', () => {
  it('should render all form fields', () => {
    render(<SkillAssessmentForm />);
    
    expect(screen.getByLabelText(/skill name/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/current level/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/confidence level/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/years of experience/i)).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();
    render(<SkillAssessmentForm />);
    
    await user.click(screen.getByRole('button', { name: /add skill/i }));
    
    expect(screen.getByText(/skill name is required/i)).toBeInTheDocument();
  });

  it('should submit valid form data', async () => {
    const mockOnSubmit = jest.fn();
    const user = userEvent.setup();
    
    render(<SkillAssessmentForm onSubmit={mockOnSubmit} />);
    
    await user.type(screen.getByLabelText(/skill name/i), 'JavaScript');
    await user.click(screen.getByRole('button', { name: /add skill/i }));
    
    expect(mockOnSubmit).toHaveBeenCalledWith({
      skillName: 'JavaScript',
      currentLevel: 5,
      confidenceLevel: 5,
      yearsExperience: 0
    });
  });
});
```

#### GapAnalysisEngine
```typescript
// Test file: __tests__/services/GapAnalysisEngine.test.ts

describe('GapAnalysisEngine', () => {
  it('should calculate readiness score correctly', () => {
    const assessments = [
      { skillName: 'JavaScript', currentLevel: 8, targetLevel: 8 },
      { skillName: 'React', currentLevel: 6, targetLevel: 8 },
      { skillName: 'Node.js', currentLevel: 4, targetLevel: 7 }
    ];
    
    const readiness = calculateReadinessScore(assessments);
    expect(readiness).toBeCloseTo(66.7, 1);
  });

  it('should identify critical gaps', () => {
    const assessments = [
      { skillName: 'JavaScript', currentLevel: 8, targetLevel: 8 },
      { skillName: 'System Design', currentLevel: 2, targetLevel: 8 }
    ];
    
    const gaps = identifyCriticalGaps(assessments);
    expect(gaps).toHaveLength(1);
    expect(gaps[0].skillName).toBe('System Design');
    expect(gaps[0].priority).toBe('critical');
  });
});
```

### Service Tests

#### AI Analysis Service
```typescript
// Test file: __tests__/services/AIAnalysisService.test.ts

describe('AIAnalysisService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should generate analysis with valid input', async () => {
    const mockGeminiResponse = {
      overallReadiness: 75,
      criticalGaps: [],
      recommendations: []
    };
    
    mockGeminiAPI.generateContent.mockResolvedValue(mockGeminiResponse);
    
    const result = await generateSkillsAnalysis({
      careerPath: 'Full Stack Developer',
      assessments: [mockAssessment]
    });
    
    expect(result.overallReadiness).toBe(75);
    expect(mockGeminiAPI.generateContent).toHaveBeenCalledTimes(1);
  });

  it('should handle AI service errors gracefully', async () => {
    mockGeminiAPI.generateContent.mockRejectedValue(new Error('API Error'));
    
    const result = await generateSkillsAnalysis({
      careerPath: 'Full Stack Developer',
      assessments: [mockAssessment]
    });
    
    expect(result.error).toBeDefined();
    expect(result.fallbackUsed).toBe(true);
  });
});
```

## Integration Testing

### API Endpoint Tests

#### Assessment Endpoints
```typescript
// Test file: __tests__/api/assessment.test.ts

describe('/api/assessment', () => {
  beforeEach(async () => {
    await cleanupDatabase();
    await seedTestData();
  });

  describe('POST /api/assessment', () => {
    it('should create new assessment', async () => {
      const response = await request(app)
        .post('/api/assessment')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          skillName: 'JavaScript',
          currentLevel: 7,
          confidenceLevel: 8,
          yearsExperience: 3
        })
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.data.skillName).toBe('JavaScript');
    });

    it('should validate input data', async () => {
      const response = await request(app)
        .post('/api/assessment')
        .set('Authorization', `Bearer ${testToken}`)
        .send({
          skillName: '',
          currentLevel: 15
        })
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
    });
  });

  describe('GET /api/assessment/:id', () => {
    it('should retrieve assessment by id', async () => {
      const assessment = await createTestAssessment();
      
      const response = await request(app)
        .get(`/api/assessment/${assessment.id}`)
        .set('Authorization', `Bearer ${testToken}`)
        .expect(200);

      expect(response.body.data.id).toBe(assessment.id);
    });
  });
});
```

#### AI Analysis Endpoints
```typescript
// Test file: __tests__/api/ai-analysis.test.ts

describe('/api/ai/skills-analysis', () => {
  it('should generate analysis with real AI service', async () => {
    const assessments = await createTestAssessments();
    
    const response = await request(app)
      .post('/api/ai/skills-analysis')
      .set('Authorization', `Bearer ${testToken}`)
      .send({
        careerPath: 'Full Stack Developer',
        assessments: assessments
      })
      .expect(200);

    expect(response.body.success).toBe(true);
    expect(response.body.data.overallReadiness).toBeGreaterThan(0);
    expect(response.body.data.criticalGaps).toBeInstanceOf(Array);
  });

  it('should handle rate limiting', async () => {
    // Make multiple rapid requests
    const promises = Array(15).fill(null).map(() =>
      request(app)
        .post('/api/ai/skills-analysis')
        .set('Authorization', `Bearer ${testToken}`)
        .send(testAnalysisRequest)
    );

    const responses = await Promise.all(promises);
    const rateLimitedResponses = responses.filter(r => r.status === 429);
    
    expect(rateLimitedResponses.length).toBeGreaterThan(0);
  });
});
```

### Database Integration Tests

```typescript
// Test file: __tests__/database/skill-assessment.test.ts

describe('SkillAssessment Database Operations', () => {
  it('should create assessment with all fields', async () => {
    const assessmentData = {
      userId: testUser.id,
      skillName: 'JavaScript',
      currentLevel: 7,
      confidenceLevel: 8,
      yearsExperience: 3,
      notes: 'Strong in ES6+ features'
    };

    const assessment = await prisma.skillAssessment.create({
      data: assessmentData
    });

    expect(assessment.id).toBeDefined();
    expect(assessment.skillName).toBe('JavaScript');
    expect(assessment.createdAt).toBeInstanceOf(Date);
  });

  it('should enforce unique constraint on user+skill', async () => {
    await createTestAssessment({ skillName: 'JavaScript' });

    await expect(
      createTestAssessment({ skillName: 'JavaScript' })
    ).rejects.toThrow('Unique constraint violation');
  });
});
```

## End-to-End Testing

### Playwright E2E Tests

```typescript
// Test file: e2e/skill-gap-analyzer.spec.ts

import { test, expect } from '@playwright/test';

test.describe('Skill Gap Analyzer E2E', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('/login');
    await page.fill('[data-testid="email"]', '<EMAIL>');
    await page.fill('[data-testid="password"]', 'TestPassword123!');
    await page.click('[data-testid="login-button"]');
    await page.waitForURL('/dashboard');
  });

  test('complete skill assessment flow', async ({ page }) => {
    // Navigate to skill gap analyzer
    await page.click('[data-testid="skills-menu"]');
    await page.click('[data-testid="gap-analyzer-link"]');
    
    // Add a skill assessment
    await page.fill('[data-testid="skill-name"]', 'JavaScript');
    await page.click('[data-testid="level-slider"]');
    await page.click('[data-testid="confidence-slider"]');
    await page.fill('[data-testid="experience-years"]', '3');
    await page.click('[data-testid="add-skill-button"]');
    
    // Verify skill was added
    await expect(page.locator('[data-testid="skill-list"]')).toContainText('JavaScript');
    
    // Generate analysis
    await page.click('[data-testid="analyze-gaps-tab"]');
    await page.selectOption('[data-testid="career-path"]', 'Full Stack Developer');
    await page.click('[data-testid="generate-analysis-button"]');
    
    // Wait for analysis to complete
    await page.waitForSelector('[data-testid="analysis-results"]', { timeout: 30000 });
    
    // Verify results
    await expect(page.locator('[data-testid="readiness-score"]')).toBeVisible();
    await expect(page.locator('[data-testid="recommendations-list"]')).toBeVisible();
  });

  test('error handling for invalid input', async ({ page }) => {
    await page.goto('/skills/gap-analyzer');
    
    // Try to submit empty form
    await page.click('[data-testid="add-skill-button"]');
    
    // Verify error messages
    await expect(page.locator('[data-testid="skill-name-error"]')).toContainText('required');
  });
});
```

## Performance Testing

### Load Testing with Artillery

```yaml
# artillery-config.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
  defaults:
    headers:
      Authorization: 'Bearer {{ $randomString() }}'

scenarios:
  - name: 'Assessment Creation'
    weight: 70
    flow:
      - post:
          url: '/api/assessment'
          json:
            skillName: 'JavaScript'
            currentLevel: '{{ $randomInt(1, 10) }}'
            confidenceLevel: '{{ $randomInt(1, 10) }}'
            yearsExperience: '{{ $randomInt(0, 20) }}'
  
  - name: 'AI Analysis'
    weight: 30
    flow:
      - post:
          url: '/api/ai/skills-analysis'
          json:
            careerPath: 'Full Stack Developer'
            assessments: [...]
```

### Performance Benchmarks

```typescript
// Test file: __tests__/performance/api-performance.test.ts

describe('API Performance', () => {
  it('should respond to assessment creation within 200ms', async () => {
    const start = Date.now();
    
    await request(app)
      .post('/api/assessment')
      .set('Authorization', `Bearer ${testToken}`)
      .send(testAssessmentData)
      .expect(201);
    
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(200);
  });

  it('should handle AI analysis within 5 seconds', async () => {
    const start = Date.now();
    
    await request(app)
      .post('/api/ai/skills-analysis')
      .set('Authorization', `Bearer ${testToken}`)
      .send(testAnalysisData)
      .expect(200);
    
    const duration = Date.now() - start;
    expect(duration).toBeLessThan(5000);
  });
});
```

## Manual Testing Procedures

### Test Scenarios

#### Scenario 1: First-Time User Journey
1. **Login** as a new user
2. **Navigate** to Skills > Gap Analyzer
3. **Complete** the onboarding flow
4. **Add** 3-5 skill assessments
5. **Generate** first analysis
6. **Review** recommendations
7. **Mark** one resource as "In Progress"

#### Scenario 2: Returning User Updates
1. **Login** as existing user with assessments
2. **Update** an existing skill level
3. **Add** a new skill
4. **Regenerate** analysis
5. **Compare** with previous results
6. **Mark** a resource as completed

#### Scenario 3: Error Handling
1. **Attempt** to submit invalid data
2. **Test** network disconnection scenarios
3. **Verify** graceful error messages
4. **Test** recovery after errors

### Browser Compatibility Testing

Test on the following browsers:
- **Chrome** (latest 2 versions)
- **Firefox** (latest 2 versions)
- **Safari** (latest 2 versions)
- **Edge** (latest 2 versions)

### Mobile Responsiveness Testing

Test on the following devices:
- **iPhone** (various sizes)
- **Android** (various sizes)
- **Tablet** (iPad, Android tablets)

## Test Data Management

### Test User Accounts
```typescript
export const testUsers = {
  newUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  },
  experiencedUser: {
    email: '<EMAIL>',
    password: 'TestPassword123!'
  }
};
```

### Sample Test Data
```typescript
export const testAssessments = [
  {
    skillName: 'JavaScript',
    currentLevel: 7,
    confidenceLevel: 8,
    yearsExperience: 3
  },
  {
    skillName: 'React',
    currentLevel: 6,
    confidenceLevel: 7,
    yearsExperience: 2
  }
];
```

## Continuous Integration

### GitHub Actions Workflow
```yaml
name: Skill Gap Analyzer Tests

on: [push, pull_request]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      
      - name: Install dependencies
        run: npm ci
      
      - name: Run unit tests
        run: npm run test:unit
      
      - name: Run integration tests
        run: npm run test:integration
      
      - name: Run E2E tests
        run: npm run test:e2e
      
      - name: Upload coverage
        uses: codecov/codecov-action@v1
```

## Test Reporting

### Coverage Reports
- **HTML Report**: Generated after each test run
- **JSON Report**: For CI/CD integration
- **LCOV Report**: For external tools

### Test Results Dashboard
- **Pass/Fail** status for all test suites
- **Performance** metrics over time
- **Coverage** trends
- **Flaky test** identification

## Troubleshooting Tests

### Common Issues
1. **Flaky E2E tests**: Add proper waits and retries
2. **Database cleanup**: Ensure proper test isolation
3. **API mocking**: Verify mock responses match real API
4. **Timing issues**: Use proper async/await patterns

### Debugging Tips
1. **Use** `--verbose` flag for detailed output
2. **Add** console.log statements for debugging
3. **Run** tests in isolation to identify issues
4. **Check** test database state between runs
