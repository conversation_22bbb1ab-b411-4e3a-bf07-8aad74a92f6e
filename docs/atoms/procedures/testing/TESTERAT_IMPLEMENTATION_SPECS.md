# 🔧 testerat Implementation Specifications

## **Technical Requirements & Known Issues**

### **🚨 Critical Issues to Catch**
Based on our Interview Practice testing, enhanced testerat MUST catch these exact issues:

1. **Authentication State Issue**
   - **Problem**: `useSession` hook loading state not handled properly
   - **File**: `src/components/interview-practice/InterviewPracticePage.tsx`
   - **Detection**: Page shows logged-out content when user is authenticated

2. **Duplicate Case Statement Bug**
   - **Problem**: Duplicate `case 3` and `case 4` in `renderStepContent` switch
   - **File**: `src/components/interview-practice/InterviewConfigurationWizard.tsx`
   - **Detection**: Next button fails on Step 3 (Interview Context)

3. **CSRF Token Header Mismatch**
   - **Problem**: Header case mismatch (`X-CSRF-Token` vs `x-csrf-token`)
   - **File**: `src/hooks/useCSRFToken.ts`
   - **Detection**: 403 Forbidden on form submission

---

## **Implementation Details**

### **🔐 Authentication Engine Specifications**

#### **Test User Configuration**
```yaml
# testerat.config.yml
authentication:
  test_users:
    basic_user:
      email: "<EMAIL>"
      password: "testpassword"
      role: "user"
      expected_redirect: "/dashboard"
    admin_user:
      email: "<EMAIL>"
      password: "admin_secure_pass"
      role: "admin"
      expected_redirect: "/admin/dashboard"
  
  timeouts:
    login_timeout: 10000
    auth_verification_timeout: 5000
    page_load_timeout: 15000
  
  auth_indicators:
    positive: 
      - '[data-testid="user-menu"]'
      - 'button:has-text("Sign Out")'
      - '.user-profile-avatar'
      - '[data-auth="authenticated"]'
    negative:
      - '.login-form'
      - 'button:has-text("Sign In")'
      - '[data-auth="unauthenticated"]'
```

#### **Authentication Engine Class Structure**
```python
class AuthenticationEngine:
    def __init__(self, config: TestConfig):
        self.config = config
        self.current_user = None
        self.auth_state = "unauthenticated"
        self.session_cookies = []
        
    async def authenticate_user(self, page, user_type="basic_user"):
        """
        CRITICAL: Must actually log in user and verify authentication
        This catches the useSession loading state issue
        """
        user_config = self.config.test_users[user_type]
        
        # 1. Navigate to login page
        await page.goto('/login')
        
        # 2. Fill login form
        await page.fill('[data-testid="email"]', user_config['email'])
        await page.fill('[data-testid="password"]', user_config['password'])
        
        # 3. Submit login
        await page.click('[data-testid="login-button"]')
        
        # 4. Wait for authentication redirect
        await page.wait_for_url(f"**{user_config['expected_redirect']}", 
                               timeout=self.config.timeouts['login_timeout'])
        
        # 5. Verify authentication state
        auth_verified = await self.verify_auth_state(page)
        if not auth_verified:
            raise AuthenticationError(f"Login failed for {user_type}")
            
        self.current_user = user_type
        self.auth_state = "authenticated"
        return True
    
    async def verify_auth_state(self, page):
        """
        CRITICAL: Multi-method authentication verification
        This catches when pages show logged-out content to authenticated users
        """
        verification_methods = []
        
        # Method 1: Check for authenticated UI elements
        for indicator in self.config.auth_indicators['positive']:
            element = await page.query_selector(indicator)
            if element:
                verification_methods.append(f"Found auth indicator: {indicator}")
                break
        
        # Method 2: Check for absence of login elements
        login_elements_found = []
        for indicator in self.config.auth_indicators['negative']:
            element = await page.query_selector(indicator)
            if element:
                login_elements_found.append(indicator)
        
        if login_elements_found:
            verification_methods.append(f"ERROR: Found login elements: {login_elements_found}")
            return False
        
        # Method 3: Check URL patterns
        current_url = page.url
        if '/login' in current_url or '/signin' in current_url:
            verification_methods.append("ERROR: Still on login page")
            return False
        
        # Method 4: Check session cookies
        cookies = await page.context.cookies()
        session_cookies = [c for c in cookies if 'session' in c['name'].lower()]
        if session_cookies:
            verification_methods.append(f"Found session cookies: {len(session_cookies)}")
        
        return len(verification_methods) > 0 and not any("ERROR" in method for method in verification_methods)
    
    async def test_protected_routes(self, page):
        """
        CRITICAL: Test that authenticated users can access protected content
        This catches the Interview Practice authentication issue
        """
        protected_routes = [
            '/interview-practice',
            '/career-planning',
            '/dashboard',
            '/profile',
            '/settings'
        ]
        
        issues = []
        for route in protected_routes:
            await page.goto(route)
            
            # Check if redirected to login (bad)
            if '/login' in page.url:
                issues.append(f"CRITICAL: Route {route} redirected to login when authenticated")
                continue
            
            # Check if showing logged-out content (bad)
            auth_content = await page.query_selector('[data-auth="authenticated"]')
            login_content = await page.query_selector('[data-auth="unauthenticated"]')
            
            if login_content and not auth_content:
                issues.append(f"CRITICAL: Route {route} showing logged-out content to authenticated user")
            
            # Specific check for Interview Practice
            if route == '/interview-practice':
                start_practice_button = await page.query_selector('[data-testid="start-new-practice"]')
                login_message = await page.query_selector(':has-text("Sign in to access")')
                
                if login_message and not start_practice_button:
                    issues.append(f"CRITICAL: Interview Practice showing login message to authenticated user")
        
        return issues
```

### **⚡ Workflow Engine Specifications**

#### **Interview Practice Workflow Configuration**
```python
INTERVIEW_PRACTICE_WORKFLOW = {
    'name': 'Interview Practice Complete Flow',
    'steps': [
        {
            'step_number': 1,
            'name': 'Practice Type Selection',
            'url_pattern': '**/interview-practice',
            'required_elements': ['[data-testid="quick-practice"]'],
            'actions': [
                {'type': 'click', 'selector': '[data-testid="quick-practice"]'},
                {'type': 'click', 'selector': 'button:has-text("Next")'}
            ],
            'validation': {
                'url_change': '**/step=2*',
                'element_visible': '[data-testid="experience-level"]'
            }
        },
        {
            'step_number': 2,
            'name': 'Experience & Difficulty',
            'required_elements': ['[data-testid="experience-level"]', '[data-testid="difficulty"]'],
            'actions': [
                {'type': 'select', 'selector': '[data-testid="experience-level"]', 'value': 'Mid Level'},
                {'type': 'select', 'selector': '[data-testid="difficulty"]', 'value': 'Medium'},
                {'type': 'click', 'selector': 'button:has-text("Next")'}
            ],
            'validation': {
                'url_change': '**/step=3*',
                'element_visible': '[data-testid="company-type"]'
            }
        },
        {
            'step_number': 3,
            'name': 'Interview Context',
            'required_elements': ['[data-testid="company-type"]', '[data-testid="interview-type"]'],
            'actions': [
                {'type': 'select', 'selector': '[data-testid="company-type"]', 'value': 'Startup'},
                {'type': 'select', 'selector': '[data-testid="interview-type"]', 'value': 'Technical Screen'},
                {'type': 'click', 'selector': 'button:has-text("Next")'}
            ],
            'validation': {
                'url_change': '**/step=4*',
                'element_visible': '[data-testid="focus-areas"]'
            },
            'critical_test': 'This is where the duplicate case bug occurred'
        },
        {
            'step_number': 4,
            'name': 'Focus Areas & Start',
            'required_elements': ['[data-testid="technical-skills"]', 'button:has-text("Start Practice")'],
            'actions': [
                {'type': 'check', 'selector': '[data-testid="technical-skills"]'},
                {'type': 'check', 'selector': '[data-testid="communication"]'},
                {'type': 'click', 'selector': 'button:has-text("Start Practice")'}
            ],
            'validation': {
                'api_call': '/api/interview-practice',
                'success_response': 200,
                'session_created': True
            },
            'critical_test': 'This is where the CSRF 403 error occurred'
        }
    ]
}
```

#### **Workflow Engine Class Structure**
```python
class WorkflowEngine:
    def __init__(self, config: TestConfig):
        self.config = config
        self.workflow_results = []
        
    async def test_interview_practice_complete_flow(self, page):
        """
        CRITICAL: Test exact workflow that was broken
        Must catch duplicate case statement bug and CSRF issues
        """
        workflow = INTERVIEW_PRACTICE_WORKFLOW
        issues = []
        
        for step in workflow['steps']:
            step_issues = await self.test_workflow_step(page, step)
            if step_issues:
                issues.extend(step_issues)
                # If critical step fails, document it specifically
                if step.get('critical_test'):
                    issues.append(f"CRITICAL STEP FAILED: {step['critical_test']}")
                break  # Stop on first failure to avoid cascading errors
        
        return issues
    
    async def test_workflow_step(self, page, step):
        """Test individual workflow step with comprehensive validation"""
        issues = []
        step_name = step['name']
        
        try:
            # 1. Verify required elements are present
            for element_selector in step['required_elements']:
                element = await page.query_selector(element_selector)
                if not element:
                    issues.append(f"Step {step_name}: Required element missing: {element_selector}")
                    return issues
            
            # 2. Execute step actions
            for action in step['actions']:
                await self.execute_action(page, action)
                await page.wait_for_timeout(500)  # Allow for UI updates
            
            # 3. Validate step completion
            validation = step.get('validation', {})
            
            # URL validation
            if 'url_change' in validation:
                try:
                    await page.wait_for_url(validation['url_change'], timeout=5000)
                except:
                    issues.append(f"Step {step_name}: URL did not change to {validation['url_change']}")
            
            # Element visibility validation
            if 'element_visible' in validation:
                element = await page.query_selector(validation['element_visible'])
                if not element:
                    issues.append(f"Step {step_name}: Expected element not visible: {validation['element_visible']}")
            
            # API call validation
            if 'api_call' in validation:
                # This will be handled by API Testing Engine
                pass
                
        except Exception as e:
            issues.append(f"Step {step_name}: Execution failed: {str(e)}")
        
        return issues
    
    async def execute_action(self, page, action):
        """Execute individual workflow action"""
        action_type = action['type']
        selector = action['selector']
        
        if action_type == 'click':
            await page.click(selector)
        elif action_type == 'select':
            await page.select_option(selector, action['value'])
        elif action_type == 'check':
            await page.check(selector)
        elif action_type == 'fill':
            await page.fill(selector, action['value'])
        else:
            raise ValueError(f"Unknown action type: {action_type}")
```

### **🛡️ API Testing Engine Specifications**

#### **CSRF Testing Configuration**
```python
CSRF_TEST_CONFIG = {
    'csrf_token_locations': [
        {'type': 'meta', 'selector': 'meta[name="csrf-token"]', 'attribute': 'content'},
        {'type': 'input', 'selector': 'input[name*="csrf"]', 'attribute': 'value'},
        {'type': 'input', 'selector': 'input[name*="_token"]', 'attribute': 'value'}
    ],
    'csrf_headers': [
        'x-csrf-token',
        'csrf-token', 
        'X-CSRF-Token',
        'CSRF-Token'
    ],
    'protected_methods': ['POST', 'PUT', 'DELETE', 'PATCH'],
    'expected_errors': {
        'missing_token': 403,
        'invalid_token': 403,
        'expired_token': 403
    }
}
```

#### **API Testing Engine Class Structure**
```python
class APITestingEngine:
    def __init__(self, config: TestConfig):
        self.config = config
        self.network_requests = []
        self.network_responses = []
        
    async def setup_network_monitoring(self, page):
        """Monitor all network requests and responses"""
        page.on('request', self.track_request)
        page.on('response', self.track_response)
    
    async def test_csrf_protection_comprehensive(self, page):
        """
        CRITICAL: Test CSRF protection - catches the 403 error we found
        """
        issues = []
        
        # 1. Check for CSRF token presence
        csrf_token = await self.find_csrf_token(page)
        if not csrf_token:
            issues.append("CRITICAL: No CSRF token found on page")
            return issues
        
        # 2. Test form submissions with CSRF
        forms = await page.query_selector_all('form')
        for form_index, form in enumerate(forms):
            form_issues = await self.test_form_csrf(page, form, csrf_token)
            issues.extend(form_issues)
        
        return issues
    
    async def find_csrf_token(self, page):
        """Find CSRF token using multiple methods"""
        for location in CSRF_TEST_CONFIG['csrf_token_locations']:
            element = await page.query_selector(location['selector'])
            if element:
                token = await element.get_attribute(location['attribute'])
                if token:
                    return token
        return None
    
    async def test_form_csrf(self, page, form, csrf_token):
        """Test individual form CSRF protection"""
        issues = []
        
        # Clear network monitoring
        self.network_requests.clear()
        self.network_responses.clear()
        
        # Submit form
        submit_button = await form.query_selector('button[type="submit"], input[type="submit"]')
        if submit_button:
            await submit_button.click()
            await page.wait_for_timeout(3000)
            
            # Analyze network requests
            for request in self.network_requests:
                if request['method'] in CSRF_TEST_CONFIG['protected_methods']:
                    csrf_issues = self.analyze_csrf_request(request, csrf_token)
                    issues.extend(csrf_issues)
        
        return issues
    
    def analyze_csrf_request(self, request, expected_token):
        """Analyze request for CSRF token issues"""
        issues = []
        
        # Check headers for CSRF token
        headers = request.get('headers', {})
        csrf_in_headers = False
        
        for header_name in CSRF_TEST_CONFIG['csrf_headers']:
            if header_name in headers:
                header_token = headers[header_name]
                if header_token == expected_token:
                    csrf_in_headers = True
                    break
                else:
                    issues.append(f"CSRF token mismatch in header {header_name}")
        
        # Check body for CSRF token
        post_data = request.get('post_data', '')
        csrf_in_body = expected_token in post_data if post_data else False
        
        if not csrf_in_headers and not csrf_in_body:
            issues.append(f"CRITICAL: CSRF token missing from {request['method']} request to {request['url']}")
        
        # Check for specific header case issues (our exact bug)
        if 'X-CSRF-Token' in headers and 'x-csrf-token' not in headers:
            issues.append("CRITICAL: CSRF header case mismatch - using 'X-CSRF-Token' but server expects 'x-csrf-token'")
        
        return issues
    
    async def track_request(self, request):
        """Track network request"""
        self.network_requests.append({
            'url': request.url,
            'method': request.method,
            'headers': request.headers,
            'post_data': request.post_data
        })
    
    async def track_response(self, response):
        """Track network response"""
        self.network_responses.append({
            'url': response.url,
            'status': response.status,
            'headers': response.headers,
            'ok': response.ok
        })
        
        # Immediate analysis for critical errors
        if response.status == 403:
            matching_request = next((req for req in self.network_requests 
                                   if req['url'] == response.url), None)
            if matching_request:
                print(f"CRITICAL: 403 Forbidden on {response.url}")
                print(f"Request headers: {matching_request['headers']}")
```

This specification provides the exact technical details needed to implement enhanced testerat that will catch all the issues we found manually. Each component is designed to detect the specific problems we encountered during Interview Practice testing.
